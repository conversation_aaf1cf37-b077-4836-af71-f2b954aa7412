import requests
import json
from config import WECOM_WEBHOOK_URL
from flask_jwt_extended import jwt_required, current_user
from flask import has_request_context

def send_wecom_message(content='test', username=None):
    """
    发送企业微信消息

    Args:
        content: 消息内容
        username: 可选的用户名，如果不提供则尝试从JWT上下文获取
    """
    try:
        # 尝试获取用户名
        if username is None:
            if has_request_context():
                try:
                    username = current_user.username
                except:
                    username = "系统"
            else:
                username = "系统"

        # 格式化消息内容
        if username == "系统":
            formatted_content = f"系统告警通知：\n{content}"
        else:
            formatted_content = f"用户{username}在运维平台操作：\n{content}"

        payload = {
            "msgtype": "text",
            "text": {
                "content": formatted_content
            }
        }

        # 发送请求
        response = requests.post(WECOM_WEBHOOK_URL, headers={"Content-Type": "application/json"}, data=json.dumps(payload))
        return response.json()

    except Exception as e:
        # 如果发送失败，返回错误信息但不抛出异常
        return {"errcode": -1, "errmsg": f"发送失败: {str(e)}"}