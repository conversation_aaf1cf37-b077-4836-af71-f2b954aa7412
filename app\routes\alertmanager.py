from flask import Blueprint, jsonify, request, current_app
from flask_jwt_extended import jwt_required
from app.models.alertmanager import AlertManagerLog
from app.services.alert_service import AlertService
from datetime import datetime, timedelta
from app import db
from app.utils.auth_utils import permission_required
from app.utils.wecom_utils import send_wecom_message
import json
from concurrent.futures import ThreadPoolExecutor
import threading

alertmanager_bp = Blueprint('alertmanager', __name__)

# 创建线程池用于并发处理webhook请求
webhook_executor = ThreadPoolExecutor(max_workers=5, thread_name_prefix="webhook-")

def parse_datetime(datetime_str):
    """解析时间字符串，支持多种格式"""
    if not datetime_str:
        return None

    # 如果已经是datetime对象，直接返回
    if isinstance(datetime_str, datetime):
        return datetime_str

    # 转换为字符串并处理
    datetime_str = str(datetime_str).strip()

    # 支持的时间格式
    formats = [
        '%Y-%m-%d %H:%M:%S',
        '%Y-%m-%dT%H:%M:%S',
        '%Y-%m-%dT%H:%M:%SZ',
        '%Y-%m-%dT%H:%M:%S.%fZ',
        '%Y/%m/%d %H:%M:%S',
        '%d/%m/%Y %H:%M:%S'
    ]

    for fmt in formats:
        try:
            return datetime.strptime(datetime_str, fmt)
        except ValueError:
            continue

    # 尝试使用 fromisoformat 处理更复杂的ISO格式
    try:
        # 处理带Z的UTC时间
        if datetime_str.endswith('Z'):
            datetime_str = datetime_str[:-1] + '+00:00'
        return datetime.fromisoformat(datetime_str)
    except:
        pass

    # 如果都解析失败，返回当前时间
    current_app.logger.warning(f"无法解析时间格式: {datetime_str}")
    return datetime.utcnow()


def process_alert_async(alert_data, app):
    """异步处理告警数据"""
    try:
        with app.app_context():
            # 解析告警数据
            alert_title = alert_data.get('alert_title', alert_data.get('title', '未知告警'))
            alert_description = alert_data.get('alert_description', alert_data.get('description', ''))
            project_owner = alert_data.get('project_owner', alert_data.get('owner', '徐世伟,郭亚彬'))
            alert_level = alert_data.get('alert_level', alert_data.get('level', alert_data.get('severity', '')))
            project_category = alert_data.get('project_category', alert_data.get('category', ''))

            # 解析时间
            alert_time = parse_datetime(alert_data.get('alert_time', alert_data.get('timestamp', alert_data.get('startsAt'))))

            # 正确处理恢复时间：只有当告警状态为 resolved 时才有恢复时间
            recovery_time = None
            if alert_data.get('status') == 'resolved':
                # 如果明确标记为已解决，使用 endsAt 作为恢复时间
                recovery_time = parse_datetime(alert_data.get('recovery_time', alert_data.get('endsAt')))
            elif alert_data.get('recovery_time'):
                # 如果有明确的恢复时间字段
                recovery_time = parse_datetime(alert_data.get('recovery_time'))

            # 如果没有传递项目负责人，使用默认值
            if not project_owner or project_owner.strip() == '':
                project_owner = '徐世伟,郭亚彬'

            # 准备告警数据用于去重检查
            processed_alert_data = {
                'alert_title': alert_title,
                'alert_description': alert_description,
                'project_owner': project_owner,
                'alert_level': alert_level,
                'project_category': project_category,
                'alert_time': alert_time,  # datetime 对象，在去重逻辑中会正确处理
                'recovery_time': recovery_time,  # datetime 对象，在去重逻辑中会正确处理
                'raw_data': alert_data
            }

            # 使用去重逻辑查找或创建告警
            alert_log, is_new = AlertManagerLog.find_or_create_alert(processed_alert_data)

            # 保存到数据库
            if is_new:
                db.session.add(alert_log)

            db.session.commit()

            if is_new:
                app.logger.info(f"创建新告警: {alert_title}, ID: {alert_log.id}")
            else:
                app.logger.info(f"更新重复告警: {alert_title}, ID: {alert_log.id}, 重复次数: {alert_log.duplicate_count}")

            # 使用智能告警服务发送通知
            # 发送通知的条件：
            # 1. 新告警（无论是否已恢复）
            # 2. 现有告警状态变为已解决
            should_send_notification = is_new or (not is_new and recovery_time is not None and alert_log.status == 'resolved')

            if should_send_notification:
                try:
                    # 准备告警数据
                    notification_data = {
                        'alert_title': alert_title,
                        'alert_description': alert_description,
                        'project_owner': project_owner,
                        'alert_level': alert_level,
                        'project_category': project_category,
                        'alert_time': alert_time.strftime('%Y-%m-%d %H:%M:%S') if alert_time else None,
                        'recovery_time': recovery_time.strftime('%Y-%m-%d %H:%M:%S') if recovery_time else None,
                        'status': alert_log.status,
                        'duplicate_count': alert_log.duplicate_count,
                        'raw_data': alert_data
                    }

                    # 发送智能告警通知
                    success, message = AlertService.send_alert_notification(notification_data, alert_log.id)
                    if success:
                        app.logger.info(f"智能告警通知发送成功: {message}")
                    else:
                        app.logger.warning(f"智能告警通知被阻止或失败: {message}")

                except Exception as e:
                    app.logger.error(f"智能告警通知处理失败: {str(e)}")
            else:
                app.logger.info(f"跳过重复告警通知: {alert_title}, 重复次数: {alert_log.duplicate_count}")

            return alert_log.id
            
    except Exception as e:
        if app:
            app.logger.error(f"处理告警数据失败: {str(e)}")
        print(f"处理告警数据失败: {str(e)}")
        try:
            db.session.rollback()
        except:
            pass
        raise


@alertmanager_bp.route('/v2/alerts', methods=['POST'])
def webhook_alert():
    """
    Alertmanager风格的webhook接收器
    接收告警数据并存储到数据库
    """
    try:
        # 获取请求数据
        if request.is_json:
            data = request.get_json()
        else:
            # 尝试解析form数据
            data = request.form.to_dict()
            if not data:
                data = request.values.to_dict()
        
        if not data:
            return jsonify({
                'success': False,
                'message': '未接收到有效数据',
                'code': 400
            }), 400
        
        current_app.logger.info(f"接收到webhook数据: {json.dumps(data, ensure_ascii=False, indent=2)}")

        # 处理Alertmanager格式的数据
        alerts = []

        # 判断数据格式
        if isinstance(data, list):
            # 直接是告警列表格式
            for alert in data:
                if isinstance(alert, dict):
                    # 保留原始数据，在 process_alert_async 中进行解析
                    alert_data = alert.copy()
                    # 添加一些标准化字段以便后续处理
                    alert_data.update({
                        'alert_title': alert.get('annotations', {}).get('summary', alert.get('labels', {}).get('alertname', '未知告警')),
                        'alert_description': alert.get('annotations', {}).get('description', ''),
                        'project_owner': alert.get('labels', {}).get('owner', alert.get('annotations', {}).get('owner', '')),
                        'alert_level': alert.get('labels', {}).get('severity', alert.get('labels', {}).get('level', '')),
                        'project_category': alert.get('labels', {}).get('category', alert.get('labels', {}).get('business_type', alert.get('labels', {}).get('service', ''))),
                    })
                    alerts.append(alert_data)
        elif isinstance(data, dict):
            if 'alerts' in data and isinstance(data['alerts'], list):
                # Alertmanager标准格式（包含alerts字段）
                for alert in data['alerts']:
                    # 保留原始数据，在 process_alert_async 中进行解析
                    alert_data = alert.copy()
                    # 添加一些标准化字段以便后续处理
                    alert_data.update({
                        'alert_title': alert.get('annotations', {}).get('summary', alert.get('labels', {}).get('alertname', '未知告警')),
                        'alert_description': alert.get('annotations', {}).get('description', ''),
                        'project_owner': alert.get('labels', {}).get('owner', alert.get('annotations', {}).get('owner', '')),
                        'alert_level': alert.get('labels', {}).get('severity', alert.get('labels', {}).get('level', '')),
                        'project_category': alert.get('labels', {}).get('category', alert.get('labels', {}).get('business_type', alert.get('labels', {}).get('service', ''))),
                    })
                    alerts.append(alert_data)
            else:
                # 单个告警或自定义格式
                alerts.append(data)
        
        # 使用线程池并发处理告警
        futures = []
        for alert_data in alerts:
            future = webhook_executor.submit(process_alert_async, alert_data, current_app._get_current_object())
            futures.append(future)
        
        # 等待所有任务完成
        processed_ids = []
        errors = []
        for future in futures:
            try:
                alert_id = future.result(timeout=30)  # 30秒超时
                processed_ids.append(alert_id)
            except Exception as e:
                errors.append(str(e))
        
        if errors:
            current_app.logger.error(f"部分告警处理失败: {errors}")
        
        return jsonify({
            'success': True,
            'message': f'成功处理 {len(processed_ids)} 条告警',
            'processed_count': len(processed_ids),
            'error_count': len(errors),
            'processed_ids': processed_ids,
            'code': 200
        })
        
    except Exception as e:
        current_app.logger.error(f"Webhook处理失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'处理失败: {str(e)}',
            'code': 500
        }), 500


@alertmanager_bp.route('/alertmanager/logs', methods=['GET'])
@jwt_required()
def get_alertmanager_logs():
    """获取AlertManager告警日志列表"""
    try:
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        status = request.args.get('status')  # active, resolved
        alert_level = request.args.get('alert_level')
        project_category = request.args.get('project_category')
        project_owner = request.args.get('project_owner')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')

        # 构建查询
        query = AlertManagerLog.query

        # 状态过滤
        if status:
            query = query.filter(AlertManagerLog.status == status)

        # 告警等级过滤
        if alert_level:
            query = query.filter(AlertManagerLog.alert_level == alert_level)

        # 项目分类过滤
        if project_category:
            query = query.filter(AlertManagerLog.project_category.like(f'%{project_category}%'))

        # 项目负责人过滤
        if project_owner:
            query = query.filter(AlertManagerLog.project_owner.like(f'%{project_owner}%'))

        # 时间范围过滤
        if start_date:
            try:
                start_dt = datetime.strptime(start_date, '%Y-%m-%d')
                query = query.filter(AlertManagerLog.alert_time >= start_dt)
            except ValueError:
                pass

        if end_date:
            try:
                end_dt = datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=1)
                query = query.filter(AlertManagerLog.alert_time < end_dt)
            except ValueError:
                pass

        # 按创建时间倒序排列
        query = query.order_by(AlertManagerLog.created_at.desc())

        # 分页
        pagination = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        # 转换为字典格式
        alert_logs = [alert.to_dict() for alert in pagination.items]

        return jsonify({
            'success': True,
            'data': {
                'items': alert_logs,
                'total': pagination.total,
                'pages': pagination.pages,
                'current_page': page,
                'per_page': per_page,
                'has_next': pagination.has_next,
                'has_prev': pagination.has_prev
            },
            'code': 200
        })

    except Exception as e:
        current_app.logger.error(f"获取AlertManager告警日志失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取AlertManager告警日志失败: {str(e)}',
            'code': 500
        }), 500


@alertmanager_bp.route('/alertmanager/logs/<int:alert_id>', methods=['GET'])
@jwt_required()
def get_alertmanager_log_detail(alert_id):
    """获取AlertManager告警日志详情"""
    try:
        alert_log = AlertManagerLog.query.get(alert_id)
        if not alert_log:
            return jsonify({
                'success': False,
                'message': 'AlertManager告警日志不存在',
                'code': 404
            }), 404

        # 包含原始数据的详细信息
        detail = alert_log.to_dict()
        if alert_log.raw_data:
            try:
                detail['raw_data'] = json.loads(alert_log.raw_data)
            except json.JSONDecodeError:
                detail['raw_data'] = alert_log.raw_data

        return jsonify({
            'success': True,
            'data': detail,
            'code': 200
        })

    except Exception as e:
        current_app.logger.error(f"获取AlertManager告警日志详情失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取AlertManager告警日志详情失败: {str(e)}',
            'code': 500
        }), 500


@alertmanager_bp.route('/alertmanager/logs/<int:alert_id>/resolve', methods=['PUT'])
@jwt_required()
@permission_required('alertmanager.resolve_alert')
def resolve_alertmanager_log(alert_id):
    """手动标记AlertManager告警为已解决"""
    try:
        alert_log = AlertManagerLog.query.get(alert_id)
        if not alert_log:
            return jsonify({
                'success': False,
                'message': 'AlertManager告警日志不存在',
                'code': 404
            }), 404

        # 更新状态和恢复时间
        alert_log.status = 'resolved'
        if not alert_log.recovery_time:
            alert_log.recovery_time = datetime.utcnow()
        alert_log.updated_at = datetime.utcnow()

        db.session.commit()

        # 使用智能告警服务发送恢复通知
        try:
            from flask_jwt_extended import current_user

            # 准备告警数据
            notification_data = {
                'alert_title': alert_log.alert_title,
                'alert_description': alert_log.alert_description + f"\n\n📝 手动标记为已解决 (操作人: {current_user.username})",
                'project_owner': alert_log.project_owner,
                'alert_level': alert_log.alert_level,
                'project_category': alert_log.project_category,
                'alert_time': alert_log.alert_time.strftime('%Y-%m-%d %H:%M:%S') if alert_log.alert_time else None,
                'recovery_time': alert_log.recovery_time.strftime('%Y-%m-%d %H:%M:%S') if alert_log.recovery_time else None,
                'status': 'resolved',
                'manual_resolve': True,  # 标记为手动解决
                'operator': current_user.username
            }

            # 发送智能告警通知
            success, message = AlertService.send_alert_notification(notification_data, alert_log.id)
            if success:
                current_app.logger.info(f"手动解决告警通知发送成功: {message}")
            else:
                current_app.logger.warning(f"手动解决告警通知发送失败: {message}")

        except Exception as e:
            current_app.logger.error(f"发送手动解决告警通知失败: {str(e)}")

        return jsonify({
            'success': True,
            'message': 'AlertManager告警已标记为解决',
            'code': 200
        })

    except Exception as e:
        current_app.logger.error(f"标记AlertManager告警解决失败: {str(e)}")
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'标记AlertManager告警解决失败: {str(e)}',
            'code': 500
        }), 500


@alertmanager_bp.route('/alertmanager/logs/stats', methods=['GET'])
@jwt_required()
def get_alertmanager_stats():
    """获取AlertManager告警统计信息"""
    try:
        # 总告警数
        total_alerts = AlertManagerLog.query.count()

        # 活跃告警数
        active_alerts = AlertManagerLog.query.filter(AlertManagerLog.status == 'active').count()

        # 已解决告警数
        resolved_alerts = AlertManagerLog.query.filter(AlertManagerLog.status == 'resolved').count()

        # 今日告警数
        today = datetime.now().date()
        today_alerts = AlertManagerLog.query.filter(
            AlertManagerLog.created_at >= today,
            AlertManagerLog.created_at < today + timedelta(days=1)
        ).count()

        # 按等级统计
        level_stats = db.session.query(
            AlertManagerLog.alert_level,
            db.func.count(AlertManagerLog.id).label('count')
        ).group_by(AlertManagerLog.alert_level).all()

        # 按分类统计
        category_stats = db.session.query(
            AlertManagerLog.project_category,
            db.func.count(AlertManagerLog.id).label('count')
        ).group_by(AlertManagerLog.project_category).all()

        # 去重统计（兼容没有去重字段的情况）
        try:
            # 尝试使用去重字段
            duplicate_alerts = AlertManagerLog.query.filter(AlertManagerLog.duplicate_count > 1).count()
            total_duplicates_result = db.session.query(
                db.func.sum(AlertManagerLog.duplicate_count - 1)
            ).scalar()
            # 确保转换为 Python 整数类型，避免 Decimal 序列化问题
            total_duplicates = int(total_duplicates_result) if total_duplicates_result else 0
        except Exception:
            # 如果去重字段不存在，使用传统方式统计
            current_app.logger.warning("去重字段不存在，使用传统方式统计重复告警")

            # 按告警标题、项目分类、告警等级分组统计重复
            duplicate_groups = db.session.query(
                AlertManagerLog.alert_title,
                AlertManagerLog.project_category,
                AlertManagerLog.alert_level,
                db.func.count(AlertManagerLog.id).label('count')
            ).group_by(
                AlertManagerLog.alert_title,
                AlertManagerLog.project_category,
                AlertManagerLog.alert_level
            ).having(db.func.count(AlertManagerLog.id) > 1).all()

            duplicate_alerts = len(duplicate_groups)  # 有重复的告警组数
            # 确保转换为 Python 整数类型，避免 Decimal 序列化问题
            total_duplicates = sum(int(group.count) - 1 for group in duplicate_groups)  # 总重复次数

        # 确保所有数据都是 JSON 可序列化的类型
        def ensure_serializable(value):
            """确保值可以被 JSON 序列化"""
            if hasattr(value, '__int__'):
                return int(value)
            elif hasattr(value, '__float__'):
                return float(value)
            else:
                return value

        return jsonify({
            'success': True,
            'data': {
                'total_alerts': ensure_serializable(total_alerts),
                'active_alerts': ensure_serializable(active_alerts),
                'resolved_alerts': ensure_serializable(resolved_alerts),
                'today_alerts': ensure_serializable(today_alerts),
                'duplicate_alerts': ensure_serializable(duplicate_alerts),
                'total_duplicates': ensure_serializable(total_duplicates),
                'level_stats': [{'level': item[0] or '未知', 'count': ensure_serializable(item[1])} for item in level_stats],
                'category_stats': [{'category': item[0] or '未知', 'count': ensure_serializable(item[1])} for item in category_stats]
            },
            'code': 200
        })

    except Exception as e:
        current_app.logger.error(f"获取AlertManager告警统计失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取AlertManager告警统计失败: {str(e)}',
            'code': 500
        }), 500


@alertmanager_bp.route('/alertmanager/logs/duplicates', methods=['GET'])
@jwt_required()
def get_duplicate_alerts():
    """获取重复告警列表"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        min_duplicates = request.args.get('min_duplicates', 2, type=int)

        # 检查是否存在去重字段
        try:
            # 尝试访问去重字段，如果不存在会抛出异常
            test_query = AlertManagerLog.query.filter(
                AlertManagerLog.duplicate_count >= min_duplicates
            ).limit(1)
            test_query.all()  # 执行查询测试字段是否存在

            # 如果字段存在，使用去重逻辑查询
            query = AlertManagerLog.query.filter(
                AlertManagerLog.duplicate_count >= min_duplicates
            ).order_by(AlertManagerLog.duplicate_count.desc(), AlertManagerLog.last_occurrence.desc())

        except Exception as field_error:
            current_app.logger.warning(f"去重字段不存在，使用传统方式查找重复告警: {str(field_error)}")

            # 如果去重字段不存在，使用传统方式查找重复告警
            # 按告警标题、项目分类、告警等级分组，找出重复的
            from sqlalchemy import func

            subquery = db.session.query(
                AlertManagerLog.alert_title,
                AlertManagerLog.project_category,
                AlertManagerLog.alert_level,
                func.count(AlertManagerLog.id).label('count')
            ).group_by(
                AlertManagerLog.alert_title,
                AlertManagerLog.project_category,
                AlertManagerLog.alert_level
            ).having(func.count(AlertManagerLog.id) >= min_duplicates).subquery()

            # 查询具体的重复告警记录
            query = AlertManagerLog.query.join(
                subquery,
                (AlertManagerLog.alert_title == subquery.c.alert_title) &
                (AlertManagerLog.project_category == subquery.c.project_category) &
                (AlertManagerLog.alert_level == subquery.c.alert_level)
            ).order_by(AlertManagerLog.created_at.desc())

        pagination = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        # 转换为字典格式
        duplicate_alerts = []
        for alert in pagination.items:
            alert_dict = alert.to_dict()

            # 如果没有去重字段，手动计算重复次数
            if not hasattr(alert, 'duplicate_count') or alert.duplicate_count is None:
                # 计算相同告警的数量
                same_alerts_count = AlertManagerLog.query.filter(
                    AlertManagerLog.alert_title == alert.alert_title,
                    AlertManagerLog.project_category == alert.project_category,
                    AlertManagerLog.alert_level == alert.alert_level
                ).count()
                alert_dict['duplicate_count'] = same_alerts_count
                alert_dict['last_occurrence'] = alert.created_at.strftime('%Y-%m-%d %H:%M:%S')
                alert_dict['alert_fingerprint'] = 'legacy'  # 标记为传统模式

            duplicate_alerts.append(alert_dict)

        return jsonify({
            'success': True,
            'data': {
                'items': duplicate_alerts,
                'total': pagination.total,
                'pages': pagination.pages,
                'current_page': page,
                'per_page': per_page,
                'has_next': pagination.has_next,
                'has_prev': pagination.has_prev
            },
            'code': 200
        })

    except Exception as e:
        current_app.logger.error(f"获取重复告警列表失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取重复告警列表失败: {str(e)}',
            'code': 500
        }), 500


@alertmanager_bp.route('/alertmanager/deduplication/config', methods=['GET', 'POST'])
@jwt_required()
def manage_deduplication_config():
    """管理去重配置"""
    if request.method == 'GET':
        # 从数据库获取当前去重配置
        try:
            from app.models.alert_config import AlertDeduplicationConfig
            config_record = AlertDeduplicationConfig.query.filter_by(is_active=True).first()

            if config_record:
                config = config_record.to_dict()
            else:
                # 返回默认配置
                config = {
                    'deduplication_window_hours': 24,
                    'fingerprint_fields': ['alert_title', 'alert_description', 'project_category', 'alert_level', 'project_owner'],
                    'description_max_length': 200,
                    'enabled': True,
                    'case_sensitive': False,
                    'normalize_whitespace': True
                }
        except ImportError:
            # 如果没有配置表，返回默认配置
            config = {
                'deduplication_window_hours': 24,
                'fingerprint_fields': ['alert_title', 'alert_description', 'project_category', 'alert_level', 'project_owner'],
                'description_max_length': 200,
                'enabled': True,
                'case_sensitive': False,
                'normalize_whitespace': True
            }

        return jsonify({
            'success': True,
            'data': config,
            'code': 200
        })

    elif request.method == 'POST':
        # 更新去重配置
        try:
            from app.models.alert_config import AlertDeduplicationConfig
            from flask_jwt_extended import current_user

            data = request.get_json()
            if not data:
                return jsonify({
                    'success': False,
                    'message': '请提供配置数据',
                    'code': 400
                }), 400

            # 验证配置参数
            if 'deduplication_window_hours' in data:
                if not isinstance(data['deduplication_window_hours'], int) or data['deduplication_window_hours'] < 1:
                    return jsonify({
                        'success': False,
                        'message': '去重时间窗口必须是大于0的整数',
                        'code': 400
                    }), 400

            if 'description_max_length' in data:
                if not isinstance(data['description_max_length'], int) or data['description_max_length'] < 50:
                    return jsonify({
                        'success': False,
                        'message': '描述最大长度必须是大于等于50的整数',
                        'code': 400
                    }), 400

            # 查找现有配置或创建新配置
            config_record = AlertDeduplicationConfig.query.filter_by(is_active=True).first()

            if config_record:
                # 更新现有配置
                if 'deduplication_window_hours' in data:
                    config_record.deduplication_window_hours = data['deduplication_window_hours']
                if 'description_max_length' in data:
                    config_record.description_max_length = data['description_max_length']
                if 'enabled' in data:
                    config_record.enabled = data['enabled']
                if 'case_sensitive' in data:
                    config_record.case_sensitive = data['case_sensitive']
                if 'normalize_whitespace' in data:
                    config_record.normalize_whitespace = data['normalize_whitespace']
                if 'fingerprint_fields' in data:
                    config_record.fingerprint_fields = ','.join(data['fingerprint_fields'])

                config_record.updated_by = current_user.username
                config_record.updated_at = datetime.utcnow()
            else:
                # 创建新配置
                config_record = AlertDeduplicationConfig(
                    config_name='默认去重配置',
                    deduplication_window_hours=data.get('deduplication_window_hours', 24),
                    description_max_length=data.get('description_max_length', 200),
                    enabled=data.get('enabled', True),
                    case_sensitive=data.get('case_sensitive', False),
                    normalize_whitespace=data.get('normalize_whitespace', True),
                    fingerprint_fields=','.join(data.get('fingerprint_fields', ['alert_title', 'alert_description', 'project_category', 'alert_level', 'project_owner'])),
                    created_by=current_user.username,
                    updated_by=current_user.username
                )
                db.session.add(config_record)

            db.session.commit()

            # 清除配置缓存（如果有的话）
            try:
                from app.utils.cache_utils import clear_deduplication_config_cache
                clear_deduplication_config_cache()
            except ImportError:
                pass

            current_app.logger.info(f"用户 {current_user.username} 更新了去重配置: {data}")

            return jsonify({
                'success': True,
                'message': '去重配置更新成功',
                'data': config_record.to_dict(),
                'code': 200
            })

        except ImportError:
            # 如果没有配置表，保存到配置文件
            return _save_config_to_file(data)
        except Exception as e:
            current_app.logger.error(f"更新去重配置失败: {str(e)}")
            db.session.rollback()
            return jsonify({
                'success': False,
                'message': f'更新去重配置失败: {str(e)}',
                'code': 500
            }), 500


def _save_config_to_file(data):
    """保存配置到文件（备用方案）"""
    import json
    import os

    try:
        config_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config', 'deduplication_runtime_config.json')

        # 确保目录存在
        os.makedirs(os.path.dirname(config_file), exist_ok=True)

        # 读取现有配置
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
        else:
            config = {}

        # 更新配置
        config.update(data)
        config['updated_at'] = datetime.utcnow().isoformat()

        # 保存配置
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)

        current_app.logger.info(f"去重配置已保存到文件: {config_file}")

        return jsonify({
            'success': True,
            'message': '去重配置更新成功（保存到文件）',
            'data': config,
            'code': 200
        })

    except Exception as e:
        current_app.logger.error(f"保存配置到文件失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'保存配置失败: {str(e)}',
            'code': 500
        }), 500


# ==================== 告警配置管理接口 ====================

@alertmanager_bp.route('/alertmanager/config/webhooks', methods=['GET'])
@jwt_required()
def get_webhook_configs():
    """获取Webhook配置列表"""
    try:
        from app.models.alert_config import AlertWebhookConfig

        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)

        pagination = AlertWebhookConfig.query.order_by(
            AlertWebhookConfig.created_at.desc()
        ).paginate(page=page, per_page=per_page, error_out=False)

        configs = [config.to_dict() for config in pagination.items]

        return jsonify({
            'success': True,
            'data': {
                'items': configs,
                'total': pagination.total,
                'pages': pagination.pages,
                'current_page': page,
                'per_page': per_page
            },
            'code': 200
        })

    except Exception as e:
        current_app.logger.error(f"获取Webhook配置失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取Webhook配置失败: {str(e)}',
            'code': 500
        }), 500


@alertmanager_bp.route('/alertmanager/config/webhooks', methods=['POST'])
@jwt_required()
@permission_required('alertmanager.manage_config')
def create_webhook_config():
    """创建Webhook配置"""
    try:
        from app.models.alert_config import AlertWebhookConfig

        data = request.get_json()

        config = AlertWebhookConfig(
            project_category=data['project_category'],
            ip_pattern=data['ip_pattern'],
            datacenter=data['datacenter'],
            environment=data['environment'],
            webhook_url=data['webhook_url'],
            group_name=data['group_name'],
            is_active=data.get('is_active', True)
        )

        db.session.add(config)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'Webhook配置创建成功',
            'data': config.to_dict(),
            'code': 200
        })

    except Exception as e:
        current_app.logger.error(f"创建Webhook配置失败: {str(e)}")
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'创建Webhook配置失败: {str(e)}',
            'code': 500
        }), 500


@alertmanager_bp.route('/alertmanager/config/webhooks/<int:webhook_id>', methods=['PUT'])
@jwt_required()
@permission_required('alertmanager.manage_config')
def update_webhook_config(webhook_id):
    """修改Webhook配置"""
    try:
        from app.models.alert_config import AlertWebhookConfig

        # 查找配置记录
        config = AlertWebhookConfig.query.get(webhook_id)
        if not config:
            return jsonify({
                'success': False,
                'message': 'Webhook配置不存在',
                'code': 404
            }), 404

        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'message': '请提供要更新的数据',
                'code': 400
            }), 400

        # 更新字段
        if 'project_category' in data:
            config.project_category = data['project_category']
        if 'ip_pattern' in data:
            config.ip_pattern = data['ip_pattern']
        if 'datacenter' in data:
            config.datacenter = data['datacenter']
        if 'environment' in data:
            config.environment = data['environment']
        if 'webhook_url' in data:
            config.webhook_url = data['webhook_url']
        if 'group_name' in data:
            config.group_name = data['group_name']
        if 'is_active' in data:
            config.is_active = data['is_active']

        # 更新时间戳
        config.updated_at = datetime.utcnow()

        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'Webhook配置更新成功',
            'data': config.to_dict(),
            'code': 200
        })

    except Exception as e:
        current_app.logger.error(f"更新Webhook配置失败: {str(e)}")
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'更新Webhook配置失败: {str(e)}',
            'code': 500
        }), 500


@alertmanager_bp.route('/alertmanager/config/webhooks/<int:webhook_id>', methods=['GET'])
@jwt_required()
def get_webhook_config_detail(webhook_id):
    """获取单个Webhook配置详情"""
    try:
        from app.models.alert_config import AlertWebhookConfig

        config = AlertWebhookConfig.query.get(webhook_id)
        if not config:
            return jsonify({
                'success': False,
                'message': 'Webhook配置不存在',
                'code': 404
            }), 404

        return jsonify({
            'success': True,
            'data': config.to_dict(),
            'code': 200
        })

    except Exception as e:
        current_app.logger.error(f"获取Webhook配置详情失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取Webhook配置详情失败: {str(e)}',
            'code': 500
        }), 500


@alertmanager_bp.route('/alertmanager/config/webhooks/<int:webhook_id>', methods=['DELETE'])
@jwt_required()
@permission_required('alertmanager.manage_config')
def delete_webhook_config(webhook_id):
    """删除Webhook配置"""
    try:
        from app.models.alert_config import AlertWebhookConfig

        config = AlertWebhookConfig.query.get(webhook_id)
        if not config:
            return jsonify({
                'success': False,
                'message': 'Webhook配置不存在',
                'code': 404
            }), 404

        # 记录删除的配置信息用于日志
        config_info = config.to_dict()

        db.session.delete(config)
        db.session.commit()

        current_app.logger.info(f"删除Webhook配置: {config_info}")

        return jsonify({
            'success': True,
            'message': 'Webhook配置删除成功',
            'code': 200
        })

    except Exception as e:
        current_app.logger.error(f"删除Webhook配置失败: {str(e)}")
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'删除Webhook配置失败: {str(e)}',
            'code': 500
        }), 500


@alertmanager_bp.route('/alertmanager/config/silence-rules', methods=['GET'])
@jwt_required()
def get_silence_rules():
    """获取静默规则列表"""
    try:
        from app.models.alert_config import AlertSilenceRule

        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)

        pagination = AlertSilenceRule.query.order_by(
            AlertSilenceRule.created_at.desc()
        ).paginate(page=page, per_page=per_page, error_out=False)

        rules = [rule.to_dict() for rule in pagination.items]

        return jsonify({
            'success': True,
            'data': {
                'items': rules,
                'total': pagination.total,
                'pages': pagination.pages,
                'current_page': page,
                'per_page': per_page
            },
            'code': 200
        })

    except Exception as e:
        current_app.logger.error(f"获取静默规则失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取静默规则失败: {str(e)}',
            'code': 500
        }), 500


@alertmanager_bp.route('/alertmanager/config/silence-rules', methods=['POST'])
@jwt_required()
@permission_required('alertmanager.manage_silence')
def create_silence_rule():
    """创建静默规则"""
    try:
        from app.models.alert_config import AlertSilenceRule
        from flask_jwt_extended import current_user

        data = request.get_json()

        start_time = datetime.utcnow()
        end_time = start_time + timedelta(hours=data['silence_hours'])

        rule = AlertSilenceRule(
            rule_name=data['rule_name'],
            alert_title_pattern=data.get('alert_title_pattern'),
            project_category=data.get('project_category'),
            alert_level=data.get('alert_level'),
            ip_pattern=data.get('ip_pattern'),
            silence_hours=data['silence_hours'],
            start_time=start_time,
            end_time=end_time,
            reason=data.get('reason'),
            created_by=current_user.username
        )

        db.session.add(rule)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '静默规则创建成功',
            'data': rule.to_dict(),
            'code': 200
        })

    except Exception as e:
        current_app.logger.error(f"创建静默规则失败: {str(e)}")
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'创建静默规则失败: {str(e)}',
            'code': 500
        }), 500


@alertmanager_bp.route('/alertmanager/config/silence-rules/<int:rule_id>', methods=['GET'])
@jwt_required()
def get_silence_rule_detail(rule_id):
    """获取单个静默规则详情"""
    try:
        from app.models.alert_config import AlertSilenceRule

        rule = AlertSilenceRule.query.get(rule_id)
        if not rule:
            return jsonify({
                'success': False,
                'message': '静默规则不存在',
                'code': 404
            }), 404

        return jsonify({
            'success': True,
            'data': rule.to_dict(),
            'code': 200
        })

    except Exception as e:
        current_app.logger.error(f"获取静默规则详情失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取静默规则详情失败: {str(e)}',
            'code': 500
        }), 500


@alertmanager_bp.route('/alertmanager/config/silence-rules/<int:rule_id>', methods=['PUT'])
@jwt_required()
@permission_required('alertmanager.manage_silence')
def update_silence_rule(rule_id):
    """修改静默规则"""
    try:
        from app.models.alert_config import AlertSilenceRule
        from flask_jwt_extended import current_user

        # 查找规则记录
        rule = AlertSilenceRule.query.get(rule_id)
        if not rule:
            return jsonify({
                'success': False,
                'message': '静默规则不存在',
                'code': 404
            }), 404

        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'message': '请提供要更新的数据',
                'code': 400
            }), 400

        # 更新字段
        if 'rule_name' in data:
            rule.rule_name = data['rule_name']
        if 'alert_title_pattern' in data:
            rule.alert_title_pattern = data['alert_title_pattern']
        if 'project_category' in data:
            rule.project_category = data['project_category']
        if 'alert_level' in data:
            rule.alert_level = data['alert_level']
        if 'ip_pattern' in data:
            rule.ip_pattern = data['ip_pattern']
        if 'reason' in data:
            rule.reason = data['reason']
        if 'is_active' in data:
            rule.is_active = data['is_active']

        # 如果更新了静默时长，重新计算结束时间
        if 'silence_hours' in data:
            rule.silence_hours = data['silence_hours']
            # 如果规则还在活跃期内，重新计算结束时间
            if rule.is_silenced():
                rule.end_time = rule.start_time + timedelta(hours=data['silence_hours'])

        # 更新时间戳
        rule.updated_at = datetime.utcnow()

        db.session.commit()

        current_app.logger.info(f"用户 {current_user.username} 更新了静默规则 {rule_id}")

        return jsonify({
            'success': True,
            'message': '静默规则更新成功',
            'data': rule.to_dict(),
            'code': 200
        })

    except Exception as e:
        current_app.logger.error(f"更新静默规则失败: {str(e)}")
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'更新静默规则失败: {str(e)}',
            'code': 500
        }), 500


@alertmanager_bp.route('/alertmanager/config/silence-rules/<int:rule_id>', methods=['DELETE'])
@jwt_required()
@permission_required('alertmanager.manage_silence')
def delete_silence_rule(rule_id):
    """删除静默规则"""
    try:
        from app.models.alert_config import AlertSilenceRule
        from flask_jwt_extended import current_user

        rule = AlertSilenceRule.query.get(rule_id)
        if not rule:
            return jsonify({
                'success': False,
                'message': '静默规则不存在',
                'code': 404
            }), 404

        # 记录删除的规则信息用于日志
        rule_info = rule.to_dict()

        db.session.delete(rule)
        db.session.commit()

        current_app.logger.info(f"用户 {current_user.username} 删除了静默规则: {rule_info}")

        return jsonify({
            'success': True,
            'message': '静默规则删除成功',
            'code': 200
        })

    except Exception as e:
        current_app.logger.error(f"删除静默规则失败: {str(e)}")
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'删除静默规则失败: {str(e)}',
            'code': 500
        }), 500


@alertmanager_bp.route('/alertmanager/config/silence-rules/<int:rule_id>/toggle', methods=['PUT'])
@jwt_required()
@permission_required('alertmanager.manage_silence')
def toggle_silence_rule(rule_id):
    """启用/禁用静默规则"""
    try:
        from app.models.alert_config import AlertSilenceRule
        from flask_jwt_extended import current_user

        rule = AlertSilenceRule.query.get(rule_id)
        if not rule:
            return jsonify({
                'success': False,
                'message': '静默规则不存在',
                'code': 404
            }), 404

        # 切换状态
        rule.is_active = not rule.is_active
        rule.updated_at = datetime.utcnow()

        db.session.commit()

        status_text = "启用" if rule.is_active else "禁用"
        current_app.logger.info(f"用户 {current_user.username} {status_text}了静默规则 {rule_id}")

        return jsonify({
            'success': True,
            'message': f'静默规则{status_text}成功',
            'data': rule.to_dict(),
            'code': 200
        })

    except Exception as e:
        current_app.logger.error(f"切换静默规则状态失败: {str(e)}")
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'切换静默规则状态失败: {str(e)}',
            'code': 500
        }), 500


@alertmanager_bp.route('/alertmanager/logs/batch-resolve', methods=['PUT'])
@jwt_required()
@permission_required('alertmanager.batch_resolve')
def batch_resolve_alerts():
    """批量标记告警为已解决"""
    try:
        data = request.get_json()
        alert_ids = data.get('alert_ids', [])

        if not alert_ids:
            return jsonify({
                'success': False,
                'message': '请提供要处理的告警ID列表',
                'code': 400
            }), 400

        # 获取要更新的告警记录（用于发送通知）
        alerts_to_resolve = AlertManagerLog.query.filter(
            AlertManagerLog.id.in_(alert_ids),
            AlertManagerLog.status == 'active'
        ).all()

        # 批量更新告警状态
        updated_count = AlertManagerLog.query.filter(
            AlertManagerLog.id.in_(alert_ids),
            AlertManagerLog.status == 'active'
        ).update({
            'status': 'resolved',
            'recovery_time': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        }, synchronize_session=False)

        db.session.commit()

        # 为每个解决的告警发送通知
        if alerts_to_resolve:
            try:
                from flask_jwt_extended import current_user

                # 使用线程池并发发送通知
                def send_batch_notification(alert):
                    try:
                        # 准备告警数据
                        notification_data = {
                            'alert_title': alert.alert_title,
                            'alert_description': alert.alert_description + f"\n\n📝 批量标记为已解决 (操作人: {current_user.username})",
                            'project_owner': alert.project_owner,
                            'alert_level': alert.alert_level,
                            'project_category': alert.project_category,
                            'alert_time': alert.alert_time.strftime('%Y-%m-%d %H:%M:%S') if alert.alert_time else None,
                            'recovery_time': datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S'),
                            'status': 'resolved',
                            'manual_resolve': True,  # 标记为手动解决
                            'batch_resolve': True,   # 标记为批量解决
                            'operator': current_user.username
                        }

                        # 发送智能告警通知
                        success, message = AlertService.send_alert_notification(notification_data, alert.id)
                        if success:
                            current_app.logger.info(f"批量解决告警通知发送成功: {alert.alert_title}")
                        else:
                            current_app.logger.warning(f"批量解决告警通知发送失败: {alert.alert_title} - {message}")

                    except Exception as e:
                        current_app.logger.error(f"发送批量解决告警通知失败: {alert.alert_title} - {str(e)}")

                # 并发发送通知
                for alert in alerts_to_resolve:
                    webhook_executor.submit(send_batch_notification, alert)

                current_app.logger.info(f"已提交 {len(alerts_to_resolve)} 个批量解决通知任务")

            except Exception as e:
                current_app.logger.error(f"批量发送解决通知失败: {str(e)}")

        return jsonify({
            'success': True,
            'message': f'成功标记 {updated_count} 条告警为已解决',
            'updated_count': updated_count,
            'code': 200
        })

    except Exception as e:
        current_app.logger.error(f"批量标记告警解决失败: {str(e)}")
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'批量标记告警解决失败: {str(e)}',
            'code': 500
        }), 500
