import subprocess
from flask import current_app
import cx_Oracle
from app.models.oracle_server import OracleServer

def get_oracle_connection(server_id):
    """获取Oracle数据库连接"""
    try:
        server = OracleServer.query.filter_by(id=server_id).first()
        if not server:
            return None, "未找到指定IP的Oracle服务器配置"

        dsn = cx_Oracle.makedsn(server.db_address, server.db_port, service_name='orcl')
        conn = cx_Oracle.connect(user=server.db_username, password=server.db_password, dsn=dsn)
        return conn, None
    except Exception as e:
        current_app.logger.error(f"Oracle连接失败: {str(e)}")
        return None, f"数据库连接失败: {str(e)}"


def get_tablespace_info(server_id):
    """
    获取Oracle表空间信息，仅限PAYMENTDB表空间
    """
    conn, error = get_oracle_connection(server_id)
    if error:
        return False, error

    try:
        cursor = conn.cursor()
        sql = """
        SELECT
            a.tablespace_name,
            ROUND(a.bytes_alloc / 1024 / 1024, 2) total_size_mb,
            ROUND(NVL(b.bytes_free, 0) / 1024 / 1024, 2) free_size_mb,
            ROUND((a.bytes_alloc - NVL(b.bytes_free, 0)) / 1024 / 1024, 2) used_size_mb,
            ROUND(NVL(b.bytes_free, 0) / a.bytes_alloc * 100, 2) free_percent,
            c.autoextensible auto_extend,
            c.file_name,
            ROUND(c.bytes / 1024 / 1024, 2) size_mb,
            ROUND(c.maxbytes / 1024 / 1024, 2) max_size_mb
        FROM (
            SELECT tablespace_name, SUM(bytes) bytes_alloc
            FROM dba_data_files
            WHERE tablespace_name = 'PAYMENTDB'
            GROUP BY tablespace_name
        ) a,
        (
            SELECT tablespace_name, SUM(bytes) bytes_free
            FROM dba_free_space
            WHERE tablespace_name = 'PAYMENTDB'
            GROUP BY tablespace_name
        ) b,
        dba_data_files c
        WHERE a.tablespace_name = b.tablespace_name (+)
        AND a.tablespace_name = c.tablespace_name
        AND c.tablespace_name = 'PAYMENTDB'
        """
        cursor.execute(sql)
        columns = [col[0].lower() for col in cursor.description]
        result = [dict(zip(columns, row)) for row in cursor.fetchall()]
        return True, result
    except Exception as e:
        current_app.logger.error(f"获取表空间信息失败: {str(e)}")
        return False, f"获取表空间信息失败: {str(e)}"
    finally:
        if conn:
            conn.close()




def extend_tablespace(server_id, tablespace_name, datafile_name=None, size_mb=1024):
    """
    扩容Oracle表空间，增加指定大小（默认1G）
    :param server_id: 服务器ID
    :param tablespace_name: 表空间名称
    :param datafile_name: 数据文件名称（可选）
    :param size_mb: 扩容大小（MB）
    """
    # 在函数开始时设置环境变量，确保正确处理Unicode字符
    import os
    os.environ["NLS_LANG"] = ".AL32UTF8"
    current_app.logger.info("已设置NLS_LANG环境变量为.AL32UTF8")
    
    conn, error = get_oracle_connection(server_id)
    if error:
        return False, error
    
    try:
        cursor = conn.cursor()
        if not isinstance(tablespace_name, str):
            tablespace_name = str(tablespace_name)
        
        if datafile_name is not None and not isinstance(datafile_name, str):
            datafile_name = str(datafile_name)
        
        if not isinstance(size_mb, int):
            try:
                size_mb = int(size_mb)
            except (ValueError, TypeError):
                size_mb = 1024  # 默认值

        check_sql = """
        SELECT COUNT(*) 
        FROM dba_data_files 
        WHERE file_name = :datafile_name 
        AND tablespace_name = :tablespace_name
        """
        
        cursor.execute(check_sql, {'datafile_name': datafile_name, 'tablespace_name': tablespace_name})
        file_exists = cursor.fetchone()[0]
        
        if file_exists == 0:
            current_app.logger.error(f"未找到指定的数据文件或表空间: {datafile_name}, {tablespace_name}")
            return False, "未找到指定的数据文件或表空间"
        
        # 获取当前文件大小
        size_sql = """
        SELECT CEIL(bytes/1024/1024) 
        FROM dba_data_files 
        WHERE file_name = :datafile_name 
        AND tablespace_name = :tablespace_name
        """
        
        cursor.execute(size_sql, {'datafile_name': datafile_name, 'tablespace_name': tablespace_name})
        current_size = cursor.fetchone()[0]
        new_size = current_size + size_mb
        
        resize_sql = "ALTER DATABASE DATAFILE '" + datafile_name + "' RESIZE " + str(new_size) + "M"
        
        
        try:
            # 使用参数绑定执行SQL，但避免字符串拼接问题
            resize_sql = f"ALTER DATABASE DATAFILE '{datafile_name}' RESIZE {new_size}M"
            cursor.execute(resize_sql)
            current_app.logger.info("SQL执行成功")
            result_message = f"数据文件 {datafile_name} 扩容成功，增加了 {size_mb}MB"
            current_app.logger.info(result_message)
        except Exception as sql_error:
            current_app.logger.error(f"SQL执行错误: {type(sql_error).__name__}: {str(sql_error)}")
            
            try:
                # 使用双引号包裹文件名的方式
                alt_sql = f'ALTER DATABASE DATAFILE "{datafile_name}" RESIZE {new_size}M'
                cursor.execute(alt_sql)
                result_message = f"数据文件 {datafile_name} 扩容成功，增加了 {size_mb}MB"
            except Exception as alt_error:
                current_app.logger.error(f"替代SQL执行错误: {type(alt_error).__name__}: {str(alt_error)}")
                try:
                    alt_sql2 = f"ALTER DATABASE DATAFILE '{datafile_name}' RESIZE {new_size}M"
                    cursor.execute(alt_sql2)
                    result_message = f"数据文件 {datafile_name} 扩容成功，增加了 {size_mb}MB"
                except Exception as alt_error2:
                    import traceback
                    current_app.logger.error(f"错误详情: {traceback.format_exc()}")
                    raise alt_error2
        
        conn.commit()
        return True, result_message
    except Exception as e:
        conn.rollback()
        current_app.logger.error(f"扩容表空间失败: {str(e)}")
        return False, f"扩容表空间失败: {str(e)}"
    finally:
        if conn:
            conn.close()

def get_disk_space(ssh_ip, ssh_username,ssh_port):
    """
    获取服务器磁盘空间信息
    返回格式化的磁盘信息列表，每个磁盘包含：
    - device: 设备名
    - total: 总容量
    - used: 已用空间
    - available: 可用空间
    - usage: 使用率
    - mount_point: 挂载点
    """
    cmd = f'ssh -p {ssh_port} {ssh_username}@{ssh_ip} "df -h|grep dev|grep -v tmpfs"'
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            disk_info = []
            for line in result.stdout.strip().split('\n'):
                if line.strip():
                    parts = line.split()
                    if len(parts) >= 6:
                        disk_info.append({
                            'device': parts[0],
                            'total': parts[1],
                            'used': parts[2],
                            'available': parts[3],
                            'usage': parts[4],
                            'mount_point': parts[5]
                        })
            return True, disk_info
        else:
            return False, f"获取磁盘空间信息失败: {result.stderr}"
    except subprocess.TimeoutExpired:
        return False, "操作超时，请检查网络连接或服务器状态"
    except Exception as e:
        current_app.logger.error(f"执行远程命令出错: {str(e)}")
        return False, f"系统错误: {str(e)}"