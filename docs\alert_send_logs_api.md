# 告警发送日志API接口文档

## 概述

本文档描述了新增的告警发送日志查看接口，这些接口允许前端清晰地查看哪条日志发送了，发送到哪个主题，以及发送的结果等详细信息。

## 数据表结构

### alertmanager_logs 表
存储告警基本信息：
- `id`: 告警ID
- `alert_title`: 告警标题
- `alert_description`: 告警描述
- `project_category`: 项目分类
- `alert_level`: 告警等级
- `project_owner`: 项目负责人
- `alert_time`: 告警时间
- `status`: 状态 (active/resolved)

### alert_send_logs 表
存储发送日志信息：
- `id`: 发送日志ID
- `alert_id`: 关联的告警ID
- `alert_fingerprint`: 告警指纹
- `group_fingerprint`: 分组指纹
- `webhook_url`: 发送的Webhook地址（主题）
- `send_time`: 发送时间
- `send_result`: 发送结果（JSON格式）
- `is_success`: 是否发送成功

## API接口

### 1. 获取发送日志列表

**接口地址**: `GET /alertmanager/send-logs`

**权限要求**: `alertmanager.view_send_logs`

**请求参数**:
- `page` (int, 可选): 页码，默认1
- `per_page` (int, 可选): 每页数量，默认20
- `is_success` (string, 可选): 过滤成功状态 ("true"/"false")
- `start_date` (string, 可选): 开始日期 (YYYY-MM-DD)
- `end_date` (string, 可选): 结束日期 (YYYY-MM-DD)
- `alert_id` (int, 可选): 告警ID过滤
- `webhook_url` (string, 可选): Webhook URL关键词过滤

**响应示例**:
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": 1,
        "alert_id": 123,
        "alert_title": "服务器CPU使用率过高",
        "alert_level": "warning",
        "project_category": "infrastructure",
        "project_owner": "徐世伟,郭亚彬",
        "webhook_url": "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=abc123...",
        "group_key": "abc123...",
        "send_time": "2024-01-15 10:30:00",
        "send_result": "{\"errcode\":0,\"errmsg\":\"ok\"}",
        "send_result_parsed": {
          "errcode": 0,
          "errmsg": "ok"
        },
        "error_code": 0,
        "error_message": "",
        "is_success": true,
        "alert_fingerprint": "hash123...",
        "group_fingerprint": "grouphash123..."
      }
    ],
    "total": 100,
    "pages": 5,
    "current_page": 1,
    "per_page": 20,
    "has_next": true,
    "has_prev": false
  },
  "code": 200
}
```

### 2. 获取发送日志详情

**接口地址**: `GET /alertmanager/send-logs/{log_id}`

**权限要求**: `alertmanager.view_send_logs`

**路径参数**:
- `log_id` (int): 发送日志ID

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "alert_id": 123,
    "webhook_url": "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=abc123...",
    "send_time": "2024-01-15 10:30:00",
    "send_result": "{\"errcode\":0,\"errmsg\":\"ok\"}",
    "send_result_parsed": {
      "errcode": 0,
      "errmsg": "ok"
    },
    "is_success": true,
    "alert_fingerprint": "hash123...",
    "group_fingerprint": "grouphash123...",
    "alert_info": {
      "id": 123,
      "alert_title": "服务器CPU使用率过高",
      "alert_description": "服务器**********的CPU使用率达到85%",
      "project_category": "infrastructure",
      "alert_level": "warning",
      "project_owner": "徐世伟,郭亚彬",
      "alert_time": "2024-01-15 10:25:00",
      "status": "active",
      "raw_data_parsed": {
        // 原始告警数据
      }
    }
  },
  "code": 200
}
```

### 3. 获取发送日志统计

**接口地址**: `GET /alertmanager/send-logs/stats`

**权限要求**: `alertmanager.view_send_logs`

**响应示例**:
```json
{
  "success": true,
  "data": {
    "total_sends": 1000,
    "success_sends": 950,
    "failed_sends": 50,
    "success_rate": 95.0,
    "today_sends": 50,
    "today_success": 48,
    "today_success_rate": 96.0,
    "hourly_stats": [
      {
        "hour": 0,
        "total": 2,
        "success": 2,
        "failed": 0
      },
      // ... 24小时数据
    ],
    "daily_stats": [
      {
        "date": "2024-01-15",
        "total": 50,
        "success": 48,
        "failed": 2
      },
      // ... 最近7天数据
    ]
  },
  "code": 200
}
```

## 字段说明

### 关键字段含义

1. **send_time**: 发送时间，显示告警何时被发送到企业微信群
2. **send_result**: 发送结果的原始JSON数据，包含企业微信API的响应
3. **is_success**: 布尔值，表示发送是否成功
4. **webhook_url**: 发送的目标Webhook地址，可以识别发送到哪个企业微信群
5. **group_key**: 从webhook_url中提取的群组标识，便于前端显示
6. **error_code**: 从send_result中解析的错误码（0表示成功）
7. **error_message**: 从send_result中解析的错误信息

### 发送结果解析

`send_result`字段存储的是企业微信API的响应JSON，典型格式：
- 成功: `{"errcode": 0, "errmsg": "ok"}`
- 失败: `{"errcode": 93000, "errmsg": "invalid webhook url"}`

接口会自动解析这个JSON并提供`send_result_parsed`、`error_code`、`error_message`字段。

## 权限配置

需要为用户分配`alertmanager.view_send_logs`权限才能访问这些接口。

管理员可以通过权限管理接口为用户授权：
```json
{
  "name": "alertmanager.view_send_logs",
  "description": "查看告警发送日志",
  "module": "alertmanager",
  "function_name": "view_send_logs"
}
```

## 使用场景

1. **监控告警发送状态**: 查看哪些告警成功发送，哪些失败
2. **排查发送问题**: 通过错误信息定位发送失败原因
3. **统计分析**: 了解告警发送的成功率和趋势
4. **审计追踪**: 追踪告警的完整生命周期

## 测试

可以使用提供的测试脚本验证接口功能：
```bash
python test_send_logs_api.py
```

确保在运行测试前：
1. 应用服务正在运行
2. 数据库中有相关数据
3. 测试用户有相应权限
