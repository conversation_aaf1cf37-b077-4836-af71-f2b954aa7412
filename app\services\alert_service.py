import re
import hashlib
import json
import requests
from datetime import datetime, timedelta
from app import db
from app.models.alert_config import (
    AlertWebhookConfig, AlertThrottleConfig, AlertSilenceRule,
    AlertSendLog
)
from flask import current_app


class AlertService:
    """告警智能处理服务"""
    
    @staticmethod
    def get_datacenter_info(ip_address):
        """根据IP地址获取机房信息"""
        if not ip_address:
            return None, None

        # 优先从 alert_webhook_configs 表中查找匹配的IP模式
        configs = AlertWebhookConfig.query.filter_by(is_active=True).all()

        for config in configs:
            if AlertService._match_ip_pattern(ip_address, config.ip_pattern):
                return config.datacenter, config.environment

        # 如果没有匹配的配置，使用默认规则
        default_mappings = {
            '10.0.2.*': ('亦庄机房', '物理机'),
            '10.0.1.*': ('亦庄机房', '物理机'),
            '10.128.0.*': ('联通云机房', '物理机'),
            '172.16.*.*': ('易畅行机房', '物理机'),
            '10.244.0.*': ('易畅行机房', '容器'),
            '10.245.*.*': ('联通云机房', '容器'),
            '10.246.*.*': ('亦庄机房', '容器')
        }

        for pattern, (datacenter, environment) in default_mappings.items():
            if AlertService._match_ip_pattern(ip_address, pattern):
                return datacenter, environment

        return '未知机房', '未知环境'
    
    @staticmethod
    def _match_ip_pattern(ip_address, pattern):
        """匹配IP地址模式"""
        # 将通配符模式转换为正则表达式
        regex_pattern = pattern.replace('.', r'\.').replace('*', r'\d+')
        regex_pattern = f'^{regex_pattern}$'
        return re.match(regex_pattern, ip_address) is not None
    
    @staticmethod
    def get_webhook_config(project_category, ip_address):
        """获取告警Webhook配置"""
        # 优先级1: 精确匹配IP模式 + 项目分类
        configs = AlertWebhookConfig.query.filter_by(
            project_category=project_category,
            is_active=True
        ).all()

        for config in configs:
            if AlertService._match_ip_pattern(ip_address, config.ip_pattern):
                return config

        # 优先级2: 通过datacenter + environment匹配
        datacenter, environment = AlertService.get_datacenter_info(ip_address)
        config = AlertWebhookConfig.query.filter_by(
            project_category=project_category,
            datacenter=datacenter,
            environment=environment,
            is_active=True
        ).first()

        if config:
            return config

        # 优先级3: 只匹配项目分类（通用配置）
        config = AlertWebhookConfig.query.filter_by(
            project_category=project_category,
            is_active=True
        ).first()

        return config
    
    @staticmethod
    def generate_alert_fingerprint(alert_data):
        """生成告警指纹，用于识别相同告警"""
        # 使用告警标题、描述、等级、分类生成指纹
        fingerprint_data = {
            'title': alert_data.get('alert_title', ''),
            'level': alert_data.get('alert_level', ''),
            'category': alert_data.get('project_category', ''),
            'description': alert_data.get('alert_description', '')[:100]  # 只取前100字符
        }
        
        fingerprint_str = json.dumps(fingerprint_data, sort_keys=True)
        return hashlib.md5(fingerprint_str.encode()).hexdigest()
    
    @staticmethod
    def generate_group_fingerprint(alert_data):
        """生成分组指纹，用于识别相同分组的告警"""
        # 使用项目分类、告警等级生成分组指纹
        group_data = {
            'category': alert_data.get('project_category', ''),
            'level': alert_data.get('alert_level', '')
        }
        
        group_str = json.dumps(group_data, sort_keys=True)
        return hashlib.md5(group_str.encode()).hexdigest()
    
    @staticmethod
    def check_silence_rules(alert_data):
        """检查告警是否被静默规则匹配"""
        rules = AlertSilenceRule.query.filter_by(is_active=True).all()
        
        for rule in rules:
            if not rule.is_silenced():
                continue
            
            # 检查各种匹配条件
            if rule.alert_title_pattern and alert_data.get('alert_title'):
                if not re.search(rule.alert_title_pattern, alert_data['alert_title']):
                    continue
            
            if rule.project_category and rule.project_category != alert_data.get('project_category'):
                continue
            
            if rule.alert_level and rule.alert_level != alert_data.get('alert_level'):
                continue
            
            if rule.ip_pattern:
                # 从告警数据中提取IP地址
                ip_address = AlertService._extract_ip_from_alert(alert_data)
                if ip_address and not AlertService._match_ip_pattern(ip_address, rule.ip_pattern):
                    continue
            
            # 如果所有条件都匹配，则被静默
            return True, rule
        
        return False, None
    
    @staticmethod
    def _extract_ip_from_alert(alert_data):
        """从告警数据中提取IP地址"""
        # 尝试从多个字段中提取IP地址
        text_fields = [
            alert_data.get('alert_title', ''),
            alert_data.get('alert_description', ''),
            str(alert_data.get('raw_data', ''))
        ]
        
        ip_pattern = r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b'
        
        for text in text_fields:
            matches = re.findall(ip_pattern, text)
            if matches:
                return matches[0]  # 返回第一个匹配的IP
        
        return None
    
    @staticmethod
    def check_throttle_limit(alert_data, webhook_config):
        """检查告警是否受限流控制"""
        # 获取限流配置
        throttle_config = AlertThrottleConfig.query.filter_by(
            project_category=alert_data.get('project_category'),
            is_active=True
        ).first()
        
        if not throttle_config:
            # 使用全局配置
            throttle_config = AlertThrottleConfig.query.filter_by(
                project_category=None,
                is_active=True
            ).first()
        
        if not throttle_config:
            return False, "无限流配置"
        
        alert_fingerprint = AlertService.generate_alert_fingerprint(alert_data)
        group_fingerprint = AlertService.generate_group_fingerprint(alert_data)
        
        now = datetime.utcnow()
        
        # 检查相同告警的重复发送间隔
        last_same_alert = AlertSendLog.query.filter_by(
            alert_fingerprint=alert_fingerprint,
            is_success=True
        ).order_by(AlertSendLog.send_time.desc()).first()
        
        if last_same_alert:
            time_diff = now - last_same_alert.send_time
            if time_diff.total_seconds() < throttle_config.repeat_interval_minutes * 60:
                return True, f"相同告警重复发送间隔限制({throttle_config.repeat_interval_minutes}分钟)"
        
        # 检查相同分组的发送间隔
        last_group_alert = AlertSendLog.query.filter_by(
            group_fingerprint=group_fingerprint,
            is_success=True
        ).order_by(AlertSendLog.send_time.desc()).first()
        
        if last_group_alert:
            time_diff = now - last_group_alert.send_time
            if time_diff.total_seconds() < throttle_config.group_interval_minutes * 60:
                return True, f"相同分组告警发送间隔限制({throttle_config.group_interval_minutes}分钟)"
        
        return False, "通过限流检查"
    
    @staticmethod
    def send_alert_notification(alert_data, alert_id):
        """发送告警通知"""
        try:
            # 提取IP地址
            ip_address = AlertService._extract_ip_from_alert(alert_data)
            
            # 获取Webhook配置
            webhook_config = AlertService.get_webhook_config(
                alert_data.get('project_category', ''),
                ip_address
            )
            
            if not webhook_config:
                return False, "未找到匹配的Webhook配置"
            
            # 检查静默规则
            is_silenced, silence_rule = AlertService.check_silence_rules(alert_data)
            if is_silenced:
                return False, f"告警被静默规则阻止: {silence_rule.rule_name}"
            
            # 检查限流
            is_throttled, throttle_reason = AlertService.check_throttle_limit(alert_data, webhook_config)
            if is_throttled:
                return False, f"告警被限流阻止: {throttle_reason}"
            
            # 构建通知消息
            datacenter, environment = AlertService.get_datacenter_info(ip_address)
            message = AlertService._build_notification_message(alert_data, datacenter, environment)
            
            # 发送通知
            result = AlertService._send_wecom_message(webhook_config.webhook_url, message)
            
            # 记录发送日志
            AlertService._log_send_result(
                alert_id, alert_data, webhook_config.webhook_url, result
            )
            
            return result.get('errcode') == 0, result.get('errmsg', '发送成功')
            
        except Exception as e:
            current_app.logger.error(f"发送告警通知失败: {str(e)}")
            return False, f"发送异常: {str(e)}"
    
    @staticmethod
    def _build_notification_message(alert_data, datacenter, environment):
        """构建通知消息"""
        # 判断告警状态和类型
        is_resolved = alert_data.get('status') == 'resolved'
        is_manual = alert_data.get('manual_resolve', False)
        is_batch = alert_data.get('batch_resolve', False)
        operator = alert_data.get('operator', '')

        # 构建状态文本
        if is_resolved:
            if is_manual:
                if is_batch:
                    status_text = f"已手动批量解决"
                else:
                    status_text = f"已手动解决"
                if operator:
                    status_text += f" (操作人: {operator})"
            else:
                status_text = "已恢复"
        else:
            status_text = "告警中"

        # 转换时间为北京时间
        alert_time_beijing = AlertService._convert_to_beijing_time(alert_data.get('alert_time'))
        recovery_time_beijing = AlertService._convert_to_beijing_time(alert_data.get('recovery_time')) if alert_data.get('recovery_time') else None

        message = f"""
🚨 {status_text}告警通知
📋 标题: {alert_data.get('alert_title', '未知')}
🏢 机房: {datacenter} - {environment}
📊 等级: {alert_data.get('alert_level', '未知')}
🏷️ 分类: {alert_data.get('project_category', '未知')}
👥 负责人: {alert_data.get('project_owner', '未知')}
⏰ 时间: {alert_time_beijing}
"""

        if alert_data.get('alert_description'):
            # 移除可能重复的手动解决信息
            description = alert_data['alert_description']
            if '📝 手动标记为已解决' in description:
                description = description.split('📝 手动标记为已解决')[0].strip()
            elif '📝 批量标记为已解决' in description:
                description = description.split('📝 批量标记为已解决')[0].strip()

            message += f"📝 描述: {description[:200]}"

        if recovery_time_beijing:
            message += f"\n✅ 恢复时间: {recovery_time_beijing}"

        # 添加重复次数信息（如果有）
        if alert_data.get('duplicate_count', 1) > 1:
            message += f"\n🔄 重复次数: {alert_data['duplicate_count']}"

        return message.strip()

    @staticmethod
    def _convert_to_beijing_time(time_str):
        """将时间字符串转换为北京时间"""
        if not time_str:
            return '未知'

        try:
            from datetime import datetime, timezone, timedelta

            # 如果已经是格式化的字符串，尝试解析
            if isinstance(time_str, str):
                # 支持的时间格式
                formats = [
                    '%Y-%m-%d %H:%M:%S',
                    '%Y-%m-%dT%H:%M:%S',
                    '%Y-%m-%dT%H:%M:%SZ',
                    '%Y-%m-%dT%H:%M:%S.%fZ',
                    '%Y-%m-%dT%H:%M:%S.%f'
                ]

                dt = None
                for fmt in formats:
                    try:
                        dt = datetime.strptime(time_str.replace('Z', ''), fmt)
                        break
                    except ValueError:
                        continue

                if dt is None:
                    # 尝试使用 fromisoformat
                    try:
                        if time_str.endswith('Z'):
                            time_str = time_str[:-1] + '+00:00'
                        dt = datetime.fromisoformat(time_str)
                    except:
                        return time_str  # 如果解析失败，返回原字符串

                # 如果没有时区信息，假设是UTC时间
                if dt.tzinfo is None:
                    dt = dt.replace(tzinfo=timezone.utc)

            elif hasattr(time_str, 'tzinfo'):
                # 如果是datetime对象
                dt = time_str
                if dt.tzinfo is None:
                    dt = dt.replace(tzinfo=timezone.utc)
            else:
                return str(time_str)

            # 转换为北京时间 (UTC+8)
            beijing_tz = timezone(timedelta(hours=8))
            beijing_time = dt.astimezone(beijing_tz)

            return beijing_time.strftime('%Y-%m-%d %H:%M:%S')

        except Exception as e:
            # 如果转换失败，记录日志并返回原字符串
            try:
                from flask import current_app
                current_app.logger.warning(f"时间转换失败: {time_str}, 错误: {str(e)}")
            except:
                pass
            return str(time_str)

    @staticmethod
    def _send_wecom_message(webhook_url, content):
        """发送企业微信消息"""
        payload = {
            "msgtype": "text",
            "text": {
                "content": content
            }
        }
        
        try:
            response = requests.post(
                webhook_url, 
                headers={"Content-Type": "application/json"}, 
                data=json.dumps(payload),
                timeout=10
            )
            return response.json()
        except Exception as e:
            return {"errcode": -1, "errmsg": f"发送失败: {str(e)}"}
    
    @staticmethod
    def _log_send_result(alert_id, alert_data, webhook_url, result):
        """记录发送结果"""
        try:
            alert_fingerprint = AlertService.generate_alert_fingerprint(alert_data)
            group_fingerprint = AlertService.generate_group_fingerprint(alert_data)
            
            send_log = AlertSendLog(
                alert_id=alert_id,
                alert_fingerprint=alert_fingerprint,
                group_fingerprint=group_fingerprint,
                webhook_url=webhook_url,
                send_result=json.dumps(result),
                is_success=result.get('errcode') == 0
            )
            
            db.session.add(send_log)
            db.session.commit()
            
        except Exception as e:
            current_app.logger.error(f"记录发送日志失败: {str(e)}")
            db.session.rollback()
