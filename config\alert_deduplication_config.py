"""
告警去重配置文件

定义不同类型告警的去重策略和规则
"""

from datetime import timedelta


class AlertDeduplicationConfig:
    """告警去重配置类"""
    
    # 默认去重配置
    DEFAULT_CONFIG = {
        'enabled': True,
        'deduplication_window_hours': 24,  # 去重时间窗口（小时）
        'fingerprint_fields': [
            'alert_title',
            'alert_description', 
            'project_category',
            'alert_level',
            'project_owner'
        ],
        'description_max_length': 200,  # 描述字段参与指纹计算的最大长度
        'case_sensitive': False,  # 是否区分大小写
        'normalize_whitespace': True,  # 是否标准化空白字符
    }
    
    # 按项目分类的特殊配置
    CATEGORY_SPECIFIC_CONFIG = {
        'database': {
            'deduplication_window_hours': 6,  # 数据库告警6小时内去重
            'fingerprint_fields': [
                'alert_title',
                'project_category',
                'alert_level'
            ],
            'description_max_length': 100,
        },
        'network': {
            'deduplication_window_hours': 12,  # 网络告警12小时内去重
            'fingerprint_fields': [
                'alert_title',
                'alert_description',
                'project_category'
            ],
            'description_max_length': 150,
        },
        'application': {
            'deduplication_window_hours': 48,  # 应用告警48小时内去重
            'fingerprint_fields': [
                'alert_title',
                'alert_description',
                'project_category',
                'alert_level',
                'project_owner'
            ],
            'description_max_length': 300,
        }
    }
    
    # 按告警等级的特殊配置
    LEVEL_SPECIFIC_CONFIG = {
        'critical': {
            'deduplication_window_hours': 2,  # 严重告警2小时内去重
            'fingerprint_fields': [
                'alert_title',
                'alert_description',
                'project_category',
                'project_owner'
            ]
        },
        'warning': {
            'deduplication_window_hours': 12,  # 警告告警12小时内去重
        },
        'info': {
            'deduplication_window_hours': 72,  # 信息告警72小时内去重
        }
    }
    
    # 告警标题模式匹配配置
    TITLE_PATTERN_CONFIG = {
        # 磁盘空间告警
        'disk_space': {
            'patterns': [
                r'磁盘空间不足',
                r'disk space.*low',
                r'filesystem.*full'
            ],
            'deduplication_window_hours': 6,
            'fingerprint_fields': ['alert_title', 'project_category']
        },
        # 内存告警
        'memory': {
            'patterns': [
                r'内存使用率.*高',
                r'memory.*usage.*high',
                r'out of memory'
            ],
            'deduplication_window_hours': 4,
            'fingerprint_fields': ['alert_title', 'project_category']
        },
        # CPU告警
        'cpu': {
            'patterns': [
                r'CPU使用率.*高',
                r'cpu.*usage.*high',
                r'high cpu load'
            ],
            'deduplication_window_hours': 4,
            'fingerprint_fields': ['alert_title', 'project_category']
        }
    }
    
    @classmethod
    def get_config_for_alert(cls, alert_data):
        """根据告警数据获取相应的去重配置"""
        config = cls.DEFAULT_CONFIG.copy()
        
        # 获取告警信息
        project_category = alert_data.get('project_category', '').lower()
        alert_level = alert_data.get('alert_level', '').lower()
        alert_title = alert_data.get('alert_title', '')
        
        # 应用分类特定配置
        if project_category in cls.CATEGORY_SPECIFIC_CONFIG:
            category_config = cls.CATEGORY_SPECIFIC_CONFIG[project_category]
            config.update(category_config)
        
        # 应用等级特定配置
        if alert_level in cls.LEVEL_SPECIFIC_CONFIG:
            level_config = cls.LEVEL_SPECIFIC_CONFIG[alert_level]
            config.update(level_config)
        
        # 应用标题模式配置
        import re
        for pattern_name, pattern_config in cls.TITLE_PATTERN_CONFIG.items():
            for pattern in pattern_config['patterns']:
                if re.search(pattern, alert_title, re.IGNORECASE):
                    # 移除patterns字段，只保留配置参数
                    pattern_config_copy = {k: v for k, v in pattern_config.items() if k != 'patterns'}
                    config.update(pattern_config_copy)
                    break
        
        return config
    
    @classmethod
    def should_deduplicate(cls, alert_data):
        """判断告警是否应该进行去重"""
        config = cls.get_config_for_alert(alert_data)
        return config.get('enabled', True)
    
    @classmethod
    def get_deduplication_window(cls, alert_data):
        """获取告警的去重时间窗口"""
        config = cls.get_config_for_alert(alert_data)
        hours = config.get('deduplication_window_hours', 24)
        return timedelta(hours=hours)
    
    @classmethod
    def get_fingerprint_fields(cls, alert_data):
        """获取用于生成指纹的字段列表"""
        config = cls.get_config_for_alert(alert_data)
        return config.get('fingerprint_fields', cls.DEFAULT_CONFIG['fingerprint_fields'])
    
    @classmethod
    def get_description_max_length(cls, alert_data):
        """获取描述字段的最大长度"""
        config = cls.get_config_for_alert(alert_data)
        return config.get('description_max_length', 200)
    
    @classmethod
    def normalize_field_value(cls, value, config=None):
        """标准化字段值"""
        if not value:
            return ''
        
        if config is None:
            config = cls.DEFAULT_CONFIG
        
        # 转换为字符串并去除首尾空白
        normalized = str(value).strip()
        
        # 标准化空白字符
        if config.get('normalize_whitespace', True):
            import re
            normalized = re.sub(r'\s+', ' ', normalized)
        
        # 大小写处理
        if not config.get('case_sensitive', False):
            normalized = normalized.lower()
        
        return normalized


# 预定义的去重规则示例
DEDUPLICATION_RULES = [
    {
        'name': '数据库连接告警',
        'description': '数据库连接相关的告警去重规则',
        'conditions': {
            'project_category': 'database',
            'alert_title_contains': ['连接', 'connection', 'timeout']
        },
        'config': {
            'deduplication_window_hours': 4,
            'fingerprint_fields': ['alert_title', 'project_category']
        }
    },
    {
        'name': '服务器资源告警',
        'description': '服务器CPU、内存、磁盘等资源告警去重规则',
        'conditions': {
            'alert_title_regex': r'(CPU|内存|磁盘|memory|disk|cpu).*使用率'
        },
        'config': {
            'deduplication_window_hours': 6,
            'fingerprint_fields': ['alert_title', 'project_category']
        }
    },
    {
        'name': '网络连通性告警',
        'description': '网络连通性相关告警去重规则',
        'conditions': {
            'project_category': 'network',
            'alert_level': ['critical', 'warning']
        },
        'config': {
            'deduplication_window_hours': 8,
            'fingerprint_fields': ['alert_title', 'alert_description', 'project_category']
        }
    }
]


def get_matching_rules(alert_data):
    """获取匹配告警数据的去重规则"""
    import re
    matching_rules = []
    
    for rule in DEDUPLICATION_RULES:
        conditions = rule['conditions']
        match = True
        
        # 检查项目分类条件
        if 'project_category' in conditions:
            if alert_data.get('project_category', '').lower() != conditions['project_category']:
                match = False
        
        # 检查告警等级条件
        if 'alert_level' in conditions:
            alert_level = alert_data.get('alert_level', '').lower()
            if isinstance(conditions['alert_level'], list):
                if alert_level not in conditions['alert_level']:
                    match = False
            else:
                if alert_level != conditions['alert_level']:
                    match = False
        
        # 检查标题包含条件
        if 'alert_title_contains' in conditions:
            alert_title = alert_data.get('alert_title', '').lower()
            contains_any = any(keyword in alert_title for keyword in conditions['alert_title_contains'])
            if not contains_any:
                match = False
        
        # 检查标题正则条件
        if 'alert_title_regex' in conditions:
            alert_title = alert_data.get('alert_title', '')
            if not re.search(conditions['alert_title_regex'], alert_title, re.IGNORECASE):
                match = False
        
        if match:
            matching_rules.append(rule)
    
    return matching_rules
