from flask import Blueprint, jsonify, request, current_app
from flask_jwt_extended import jwt_required
from app.models.logalert import Log<PERSON>lert
from datetime import datetime,timedelta
from app import db
from app.utils.auth_utils import permission_required
from app.utils.wecom_utils import send_wecom_message

logalert_bp = Blueprint('logalert', __name__)

@logalert_bp.route('/logalert', methods=['GET'])
@jwt_required()
def get_logalert():
    # 从数据库获取所有日志报警关键词
    alerts = LogAlert.query.all()
    alert_list = []
    
    for alert in alerts:
        alert_list.append({
            'id': alert.id,
            'type': alert.type,
            'value': alert.value,
            'created_at': alert.created_at.strftime('%Y-%m-%d %H:%M:%S') if alert.created_at else None
        })
    
    return jsonify({
        'success': True,
        'data': alert_list,
        'code': 200
    })

@logalert_bp.route('/logalert/add', methods=['POST'])
@jwt_required()
@permission_required('logalert.create_log_alert')
def logalert_add():
    data = request.get_json()
    alert_type = data.get('type')
    value = data.get('value')
    created_at = datetime.utcnow() + timedelta(hours=8)
    
    if not alert_type or not value:
        return jsonify({
            'success': False,
            'message': '类型和值不能为空',
            'code': 400
        }), 400
        
    alert = LogAlert(type=alert_type, value=value, created_at=created_at)
    db.session.add(alert)
    try:
        db.session.commit()
        send_wecom_message(f"添加{alert_type}名单日志报警关键词：{value}")
        return jsonify({
            'success': True,
            'message': '添加成功',
            'code': 200
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'添加失败：{str(e)}',
            'code': 500
        }), 500

@logalert_bp.route('/logalert/delete/<int:id>', methods=['DELETE'])
@jwt_required()
@permission_required('logalert.delete_log_alert')
def logalert_delete(id):   
    # 先获取要删除的记录信息，用于通知消息
    alert = LogAlert.query.get(id)
    if not alert:
        return jsonify({
            'success': False,
            'message': '关键词不存在',
            'code': 404
        }), 404
    
    # 保存信息用于通知
    alert_type = alert.type
    alert_value = alert.value
        
    try:
        # 添加日志记录删除操作
        current_app.logger.info(f"尝试删除LogAlert记录，ID: {id}, 类型: {alert_type}, 值: {alert_value}")
        
        # 使用对象删除方式替代查询删除
        db.session.delete(alert)
        db.session.flush()  # 确保在提交前执行删除操作
        db.session.commit()
        
        # 验证删除是否成功
        check_alert = LogAlert.query.get(id)
        if check_alert:
            current_app.logger.error(f"删除操作未生效，记录仍然存在，ID: {id}")
            # 再次尝试删除
            db.session.delete(check_alert)
            db.session.commit()
            current_app.logger.info(f"二次尝试删除完成，ID: {id}")
        
        send_wecom_message(f"删除{alert_type}名单日志报警关键词：{alert_value}")
        
        return jsonify({
            'success': True,
            'message': '删除成功',
            'code': 200
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'删除失败：{str(e)}',
            'code': 500
        }), 500
