from app import db
from datetime import datetime, timedelta
import json


class AlertWebhookConfig(db.Model):
    """告警Webhook配置表 - 根据项目分类和IP地址配置不同的企业微信群"""
    __tablename__ = 'alert_webhook_configs'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    project_category = db.Column(db.String(100), nullable=False, comment='项目分类')
    ip_pattern = db.Column(db.String(50), nullable=False, comment='IP地址模式，如10.0.2.*')
    datacenter = db.Column(db.String(50), nullable=False, comment='机房名称')
    environment = db.Column(db.String(50), nullable=False, comment='环境类型')
    webhook_url = db.Column(db.String(500), nullable=False, comment='企业微信Webhook地址')
    group_name = db.Column(db.String(200), nullable=False, comment='群组名称')
    is_active = db.Column(db.<PERSON>, default=True, comment='是否启用')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    def to_dict(self):
        return {
            'id': self.id,
            'project_category': self.project_category,
            'ip_pattern': self.ip_pattern,
            'datacenter': self.datacenter,
            'environment': self.environment,
            'webhook_url': self.webhook_url,
            'group_name': self.group_name,
            'is_active': self.is_active,
            'created_at': self._format_beijing_time(self.created_at),
            'updated_at': self._format_beijing_time(self.updated_at)
        }

    def _format_beijing_time(self, dt):
        """将datetime对象格式化为北京时间字符串"""
        if not dt:
            return None

        try:
            from datetime import timezone, timedelta

            # 如果没有时区信息，假设是UTC时间
            if dt.tzinfo is None:
                dt = dt.replace(tzinfo=timezone.utc)

            # 转换为北京时间 (UTC+8)
            beijing_tz = timezone(timedelta(hours=8))
            beijing_time = dt.astimezone(beijing_tz)

            return beijing_time.strftime('%Y-%m-%d %H:%M:%S')

        except Exception:
            # 如果转换失败，返回原格式
            return dt.strftime('%Y-%m-%d %H:%M:%S') if dt else None


class AlertThrottleConfig(db.Model):
    """告警限流配置表 - 控制告警发送频率"""
    __tablename__ = 'alert_throttle_configs'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    config_name = db.Column(db.String(100), nullable=False, comment='配置名称')
    project_category = db.Column(db.String(100), nullable=True, comment='项目分类，为空表示全局配置')
    group_interval_minutes = db.Column(db.Integer, default=5, comment='相同分组告警发送间隔(分钟)')
    repeat_interval_minutes = db.Column(db.Integer, default=60, comment='相同告警重复发送间隔(分钟)')
    is_active = db.Column(db.Boolean, default=True, comment='是否启用')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    def to_dict(self):
        return {
            'id': self.id,
            'config_name': self.config_name,
            'project_category': self.project_category,
            'group_interval_minutes': self.group_interval_minutes,
            'repeat_interval_minutes': self.repeat_interval_minutes,
            'is_active': self.is_active,
            'created_at': self._format_beijing_time(self.created_at),
            'updated_at': self._format_beijing_time(self.updated_at)
        }

    def _format_beijing_time(self, dt):
        """将datetime对象格式化为北京时间字符串"""
        if not dt:
            return None

        try:
            from datetime import timezone, timedelta

            # 如果没有时区信息，假设是UTC时间
            if dt.tzinfo is None:
                dt = dt.replace(tzinfo=timezone.utc)

            # 转换为北京时间 (UTC+8)
            beijing_tz = timezone(timedelta(hours=8))
            beijing_time = dt.astimezone(beijing_tz)

            return beijing_time.strftime('%Y-%m-%d %H:%M:%S')

        except Exception:
            # 如果转换失败，返回原格式
            return dt.strftime('%Y-%m-%d %H:%M:%S') if dt else None


class AlertSilenceRule(db.Model):
    """告警静默规则表"""
    __tablename__ = 'alert_silence_rules'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    rule_name = db.Column(db.String(200), nullable=False, comment='规则名称')
    alert_title_pattern = db.Column(db.String(500), nullable=True, comment='告警标题匹配模式')
    project_category = db.Column(db.String(100), nullable=True, comment='项目分类')
    alert_level = db.Column(db.String(50), nullable=True, comment='告警等级')
    ip_pattern = db.Column(db.String(50), nullable=True, comment='IP地址模式')
    silence_hours = db.Column(db.Integer, nullable=False, comment='静默时长(小时)')
    start_time = db.Column(db.DateTime, nullable=False, comment='静默开始时间')
    end_time = db.Column(db.DateTime, nullable=False, comment='静默结束时间')
    reason = db.Column(db.Text, nullable=True, comment='静默原因')
    created_by = db.Column(db.String(100), nullable=False, comment='创建人')
    is_active = db.Column(db.Boolean, default=True, comment='是否启用')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    def is_silenced(self):
        """检查当前时间是否在静默期内"""
        now = datetime.utcnow()
        return self.is_active and self.start_time <= now <= self.end_time
    
    def to_dict(self):
        return {
            'id': self.id,
            'rule_name': self.rule_name,
            'alert_title_pattern': self.alert_title_pattern,
            'project_category': self.project_category,
            'alert_level': self.alert_level,
            'ip_pattern': self.ip_pattern,
            'silence_hours': self.silence_hours,
            'start_time': self._format_beijing_time(self.start_time),
            'end_time': self._format_beijing_time(self.end_time),
            'reason': self.reason,
            'created_by': self.created_by,
            'is_active': self.is_active,
            'is_silenced': self.is_silenced(),
            'created_at': self._format_beijing_time(self.created_at),
            'updated_at': self._format_beijing_time(self.updated_at)
        }

    def _format_beijing_time(self, dt):
        """将datetime对象格式化为北京时间字符串"""
        if not dt:
            return None

        try:
            from datetime import timezone, timedelta

            # 如果没有时区信息，假设是UTC时间
            if dt.tzinfo is None:
                dt = dt.replace(tzinfo=timezone.utc)

            # 转换为北京时间 (UTC+8)
            beijing_tz = timezone(timedelta(hours=8))
            beijing_time = dt.astimezone(beijing_tz)

            return beijing_time.strftime('%Y-%m-%d %H:%M:%S')

        except Exception:
            # 如果转换失败，返回原格式
            return dt.strftime('%Y-%m-%d %H:%M:%S') if dt else None


class AlertSendLog(db.Model):
    """告警发送日志表 - 记录告警发送历史，用于限流控制"""
    __tablename__ = 'alert_send_logs'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    alert_id = db.Column(db.Integer, nullable=False, comment='告警ID')
    alert_fingerprint = db.Column(db.String(200), nullable=False, comment='告警指纹，用于识别相同告警')
    group_fingerprint = db.Column(db.String(200), nullable=False, comment='分组指纹，用于识别相同分组')
    webhook_url = db.Column(db.String(500), nullable=False, comment='发送的Webhook地址')
    send_time = db.Column(db.DateTime, default=datetime.utcnow, nullable=False, comment='发送时间')
    send_result = db.Column(db.Text, nullable=True, comment='发送结果')
    is_success = db.Column(db.Boolean, default=True, comment='是否发送成功')
    
    def to_dict(self):
        return {
            'id': self.id,
            'alert_id': self.alert_id,
            'alert_fingerprint': self.alert_fingerprint,
            'group_fingerprint': self.group_fingerprint,
            'webhook_url': self.webhook_url,
            'send_time': self._format_beijing_time(self.send_time),
            'send_result': self.send_result,
            'is_success': self.is_success
        }

    def _format_beijing_time(self, dt):
        """将datetime对象格式化为北京时间字符串"""
        if not dt:
            return None

        try:
            from datetime import timezone, timedelta

            # 如果没有时区信息，假设是UTC时间
            if dt.tzinfo is None:
                dt = dt.replace(tzinfo=timezone.utc)

            # 转换为北京时间 (UTC+8)
            beijing_tz = timezone(timedelta(hours=8))
            beijing_time = dt.astimezone(beijing_tz)

            return beijing_time.strftime('%Y-%m-%d %H:%M:%S')

        except Exception:
            # 如果转换失败，返回原格式
            return dt.strftime('%Y-%m-%d %H:%M:%S') if dt else None


class AlertDeduplicationConfig(db.Model):
    """告警去重配置表"""
    __tablename__ = 'alert_deduplication_configs'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    config_name = db.Column(db.String(100), nullable=False, comment='配置名称')
    deduplication_window_hours = db.Column(db.Integer, default=24, comment='去重时间窗口(小时)')
    description_max_length = db.Column(db.Integer, default=200, comment='描述字段最大长度')
    enabled = db.Column(db.Boolean, default=True, comment='是否启用去重')
    case_sensitive = db.Column(db.Boolean, default=False, comment='是否区分大小写')
    normalize_whitespace = db.Column(db.Boolean, default=True, comment='是否标准化空白字符')
    fingerprint_fields = db.Column(db.Text, nullable=True, comment='参与指纹计算的字段，逗号分隔')
    created_by = db.Column(db.String(100), nullable=False, comment='创建人')
    updated_by = db.Column(db.String(100), nullable=False, comment='更新人')
    is_active = db.Column(db.Boolean, default=True, comment='是否为当前活跃配置')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    def to_dict(self):
        return {
            'id': self.id,
            'config_name': self.config_name,
            'deduplication_window_hours': self.deduplication_window_hours,
            'description_max_length': self.description_max_length,
            'enabled': self.enabled,
            'case_sensitive': self.case_sensitive,
            'normalize_whitespace': self.normalize_whitespace,
            'fingerprint_fields': self.fingerprint_fields.split(',') if self.fingerprint_fields else [],
            'created_by': self.created_by,
            'updated_by': self.updated_by,
            'is_active': self.is_active,
            'created_at': self._format_beijing_time(self.created_at),
            'updated_at': self._format_beijing_time(self.updated_at)
        }

    def _format_beijing_time(self, dt):
        """将datetime对象格式化为北京时间字符串"""
        if not dt:
            return None

        try:
            from datetime import timezone, timedelta

            # 如果没有时区信息，假设是UTC时间
            if dt.tzinfo is None:
                dt = dt.replace(tzinfo=timezone.utc)

            # 转换为北京时间 (UTC+8)
            beijing_tz = timezone(timedelta(hours=8))
            beijing_time = dt.astimezone(beijing_tz)

            return beijing_time.strftime('%Y-%m-%d %H:%M:%S')

        except Exception:
            # 如果转换失败，返回原格式
            return dt.strftime('%Y-%m-%d %H:%M:%S') if dt else None


class DatacenterMapping(db.Model):
    """机房IP地址映射表"""
    __tablename__ = 'datacenter_mappings'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    ip_pattern = db.Column(db.String(50), nullable=False, unique=True, comment='IP地址模式')
    datacenter = db.Column(db.String(50), nullable=False, comment='机房名称')
    environment = db.Column(db.String(50), nullable=False, comment='环境类型')
    description = db.Column(db.String(200), nullable=True, comment='描述')
    is_active = db.Column(db.Boolean, default=True, comment='是否启用')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    def to_dict(self):
        return {
            'id': self.id,
            'ip_pattern': self.ip_pattern,
            'datacenter': self.datacenter,
            'environment': self.environment,
            'description': self.description,
            'is_active': self.is_active,
            'created_at': self._format_beijing_time(self.created_at),
            'updated_at': self._format_beijing_time(self.updated_at)
        }

    def _format_beijing_time(self, dt):
        """将datetime对象格式化为北京时间字符串"""
        if not dt:
            return None

        try:
            from datetime import timezone, timedelta

            # 如果没有时区信息，假设是UTC时间
            if dt.tzinfo is None:
                dt = dt.replace(tzinfo=timezone.utc)

            # 转换为北京时间 (UTC+8)
            beijing_tz = timezone(timedelta(hours=8))
            beijing_time = dt.astimezone(beijing_tz)

            return beijing_time.strftime('%Y-%m-%d %H:%M:%S')

        except Exception:
            # 如果转换失败，返回原格式
            return dt.strftime('%Y-%m-%d %H:%M:%S') if dt else None
