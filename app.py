#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
开发模式入口文件
"""

if __name__ == '__main__':
    from app import create_app, db

    # 创建应用实例
    application = create_app()

    # 初始化数据库
    with application.app_context():
        try:
            db.create_all()
            print("数据库表初始化完成")
        except Exception as e:
            print(f"数据库表初始化失败: {e}")

    # 启动开发服务器
    application.run(
        debug=application.config.get('DEBUG', False),
        host='0.0.0.0',
        port=5555
    )