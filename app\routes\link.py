from flask import Blueprint, jsonify, request, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from app.models.link import Link
from app.models.user import User
from app.utils.db import db, session_scope
from app.utils.auth_utils import permission_required
from sqlalchemy import desc, distinct, or_
import os
import uuid
from werkzeug.utils import secure_filename

link_bp = Blueprint('link', __name__)

@link_bp.route('/links', methods=['GET'])
def get_links():
    try:
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        category = request.args.get('category')
        environment = request.args.get('environment')
        search = request.args.get('search')  # 新增搜索功能

        # 参数验证
        if page < 1:
            page = 1
        if per_page < 1 or per_page > 100:  # 限制每页最大数量
            per_page = 20

        with session_scope() as session:
            # 构建查询
            query = session.query(Link)

            # 应用过滤条件
            if category:
                query = query.filter(Link.category == category)
            if environment:
                query = query.filter(Link.environment == environment)
            if search:
                # 在标题、描述中搜索
                search_pattern = f'%{search}%'
                query = query.filter(
                    or_(
                        Link.title.like(search_pattern),
                        Link.description.like(search_pattern)
                    )
                )

            # 按创建时间降序排序
            query = query.order_by(desc(Link.created_at))

            # 获取总数
            total = query.count()

            # 计算分页信息
            offset = (page - 1) * per_page
            pages = (total + per_page - 1) // per_page  # 向上取整
            has_next = page < pages
            has_prev = page > 1

            # 分页查询
            links = query.offset(offset).limit(per_page).all()

            # 转换为字典格式
            result = [link.to_dict() for link in links]

            return jsonify({
                'success': True,
                'message': '获取链接列表成功',
                'data': {
                    'items': result,
                    'total': total,
                    'pages': pages,
                    'current_page': page,
                    'per_page': per_page,
                    'has_next': has_next,
                    'has_prev': has_prev
                },
                'code': 200
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取链接列表失败: {str(e)}',
            'code': 500
        }), 500

@link_bp.route('/links/<int:link_id>', methods=['GET'])
@jwt_required()
def get_link(link_id):
    try:
        with session_scope() as session:
            link = session.query(Link).get(link_id)
            
            if not link:
                return jsonify({
                    'success': False,
                    'message': '链接不存在',
                    'code': 404
                }), 404
            
            return jsonify({
                'success': True,
                'message': '获取链接详情成功',
                'data': link.to_dict(),
                'code': 200
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取链接详情失败: {str(e)}',
            'code': 500
        }), 500

@link_bp.route('/links', methods=['POST'])
@jwt_required()
@permission_required('link.create_link')
def create_link():
    try:
        # 获取当前用户信息
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        if not user:
            return jsonify({
                'success': False,
                'message': '未授权的访问',
                'code': 401
            }), 401
        
        # 检查是否是表单数据
        if request.content_type and 'multipart/form-data' in request.content_type:
            # 获取表单数据
            title = request.form.get('title')
            url = request.form.get('url')
            category = request.form.get('category')
            description = request.form.get('description')
            environment = request.form.get('environment')
            icon = 'fa-link'  # 默认图标
            
            # 处理上传的图标文件
            if 'icon_file' in request.files and request.files['icon_file'].filename:
                icon_file = request.files['icon_file']
                # 确保上传目录存在
                upload_dir = os.path.join(current_app.static_folder, 'uploads', 'icons')
                os.makedirs(upload_dir, exist_ok=True)
                
                # 使用标题作为文件名，确保文件名安全
                filename = secure_filename(title)
                # 获取文件扩展名
                _, file_extension = os.path.splitext(icon_file.filename)
                # 完整的文件名
                full_filename = filename + file_extension
                # 保存文件
                file_path = os.path.join(upload_dir, full_filename)
                icon_file.save(file_path)
                
                # 设置图标路径为相对路径
                icon = f'/static/uploads/icons/{full_filename}'
        else:
            # 获取JSON数据
            data = request.json
            if not data:
                return jsonify({
                    'success': False,
                    'message': '请提供链接数据',
                    'code': 400
                }), 400
            
            # 验证必填字段
            if 'title' not in data or 'url' not in data:
                return jsonify({
                    'success': False,
                    'message': '标题和URL为必填项',
                    'code': 400
                }), 400
            
            title = data['title']
            url = data['url']
            category = data.get('category')
            description = data.get('description')
            icon = data.get('icon', 'fa-link')
            environment = data.get('environment')
        
        # 验证必填字段
        if not title or not url:
            return jsonify({
                'success': False,
                'message': '标题和URL为必填项',
                'code': 400
            }), 400
        
        # 创建新链接
        with session_scope() as session:
            new_link = Link(
                title=title,
                url=url,
                category=category,
                description=description,
                icon=icon,
                environment=environment
                # created_at 字段会自动使用当前时间
            )
            
            session.add(new_link)
            session.flush()  # 刷新以获取ID
            
            return jsonify({
                'success': True,
                'message': '创建链接成功',
                'data': new_link.to_dict(),
                'code': 201
            }), 201
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'创建链接失败: {str(e)}',
            'code': 500
        }), 500

@link_bp.route('/links/<int:link_id>', methods=['PUT'])
@jwt_required()
@permission_required('link.update_link')
def update_link(link_id):
    try:
        # 获取当前用户信息
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        if not user:
            return jsonify({
                'success': False,
                'message': '未授权的访问',
                'code': 401
            }), 401
        
        with session_scope() as session:
            link = session.query(Link).get(link_id)
            
            if not link:
                return jsonify({
                    'success': False,
                    'message': '链接不存在',
                    'code': 404
                }), 404
            
            # 检查是否是表单数据
            if request.content_type and 'multipart/form-data' in request.content_type:
                # 获取表单数据
                if 'title' in request.form:
                    link.title = request.form.get('title')
                if 'url' in request.form:
                    link.url = request.form.get('url')
                if 'category' in request.form:
                    link.category = request.form.get('category')
                if 'description' in request.form:
                    link.description = request.form.get('description')
                if 'environment' in request.form:
                    link.environment = request.form.get('environment')
                
                # 处理上传的图标文件
                if 'icon_file' in request.files and request.files['icon_file'].filename:
                    icon_file = request.files['icon_file']
                    # 确保上传目录存在
                    upload_dir = os.path.join(current_app.static_folder, 'uploads', 'icons')
                    os.makedirs(upload_dir, exist_ok=True)
                    
                    # 使用标题作为文件名，确保文件名安全
                    filename = secure_filename(link.title)
                    # 获取文件扩展名
                    _, file_extension = os.path.splitext(icon_file.filename)
                    # 完整的文件名
                    full_filename = filename + file_extension
                    # 保存文件
                    file_path = os.path.join(upload_dir, full_filename)
                    icon_file.save(file_path)
                    
                    # 设置图标路径为相对路径
                    link.icon = f'/static/uploads/icons/{full_filename}'
            else:
                # 获取JSON数据
                data = request.json
                if not data:
                    return jsonify({
                        'success': False,
                        'message': '请提供更新数据',
                        'code': 400
                    }), 400
                
                # 更新字段
                if 'title' in data:
                    link.title = data['title']
                if 'url' in data:
                    link.url = data['url']
                if 'category' in data:
                    link.category = data['category']
                if 'description' in data:
                    link.description = data['description']
                if 'icon' in data:
                    link.icon = data['icon']
                if 'environment' in data:
                    link.environment = data['environment']
            
            return jsonify({
                'success': True,
                'message': '更新链接成功',
                'data': link.to_dict(),
                'code': 200
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'更新链接失败: {str(e)}',
            'code': 500
        }), 500

@link_bp.route('/links/<int:link_id>', methods=['DELETE'])
@jwt_required()
@permission_required('link.delete_link')
def delete_link(link_id):
    try:
        # 获取当前用户信息
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        if not user:
            return jsonify({
                'success': False,
                'message': '未授权的访问',
                'code': 401
            }), 401
        
        with session_scope() as session:
            link = session.query(Link).get(link_id)
            
            if not link:
                return jsonify({
                    'success': False,
                    'message': '链接不存在',
                    'code': 404
                }), 404
            
            session.delete(link)
            
            return jsonify({
                'success': True,
                'message': '删除链接成功',
                'code': 200
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'删除链接失败: {str(e)}',
            'code': 500
        }), 500

@link_bp.route('/links/environments', methods=['GET'])
@jwt_required()
def get_environments():
    try:
        with session_scope() as session:
            # 查询所有不同的环境值
            environments = session.query(distinct(Link.environment)).all()
            
            # 提取环境值并过滤掉None值
            environment_list = [env[0] for env in environments if env[0] is not None]
            
            return jsonify({
                'success': True,
                'message': '获取环境列表成功',
                'data': environment_list,
                'code': 200
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取环境列表失败: {str(e)}',
            'code': 500
        }), 500

@link_bp.route('/links/icons', methods=['GET'])
@jwt_required()
def get_icons():
    try:
        # 获取图标目录路径
        icons_dir = os.path.join(current_app.static_folder, 'uploads', 'icons')
        
        # 确保目录存在
        os.makedirs(icons_dir, exist_ok=True)
        
        # 获取目录中的所有文件
        icon_files = []
        for filename in os.listdir(icons_dir):
            if os.path.isfile(os.path.join(icons_dir, filename)):
                # 构建图标的URL路径
                icon_url = f'/static/uploads/icons/{filename}'
                icon_files.append({
                    'name': filename,
                    'url': icon_url
                })
        
        return jsonify({
            'success': True,
            'message': '获取图标列表成功',
            'data': icon_files,
            'code': 200
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取图标列表失败: {str(e)}',
            'code': 500
        }), 500

@link_bp.route('/upload/icon', methods=['POST'])
@jwt_required()
@permission_required('link.upload_icon')
def upload_icon():
    try:
        # 获取当前用户信息
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        if not user:
            return jsonify({
                'success': False,
                'message': '未授权的访问',
                'code': 401
            }), 401
        
        # 检查是否有文件上传
        if 'file' not in request.files or not request.files['file'].filename:
            return jsonify({
                'success': False,
                'message': '请选择要上传的图标文件',
                'code': 400
            }), 400
        
        icon_file = request.files['file']
        
        # 检查文件类型
        allowed_extensions = {'png', 'jpg', 'jpeg', 'gif', 'svg', 'ico'}
        file_extension = icon_file.filename.rsplit('.', 1)[1].lower() if '.' in icon_file.filename else ''
        
        if file_extension not in allowed_extensions:
            return jsonify({
                'success': False,
                'message': f'不支持的文件类型，允许的类型: {", ".join(allowed_extensions)}',
                'code': 400
            }), 400
        
        # 确保上传目录存在
        icons_dir = os.path.join(current_app.static_folder, 'uploads', 'icons')
        os.makedirs(icons_dir, exist_ok=True)
        
        # 生成唯一文件名
        filename = secure_filename(request.form.get('filename', ''))
        if not filename:
            # 如果没有提供文件名，使用UUID生成唯一文件名
            filename = str(uuid.uuid4())
        
        # 完整的文件名
        full_filename = filename + '.' + file_extension
        
        # 保存文件
        file_path = os.path.join(icons_dir, full_filename)
        icon_file.save(file_path)
        
        # 返回图标URL
        icon_url = f'/static/uploads/icons/{full_filename}'
        
        return jsonify({
            'success': True,
            'message': '上传图标成功',
            'data': {
                'name': full_filename,
                'url': icon_url
            },
            'code': 201
        }), 201
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'上传图标失败: {str(e)}',
            'code': 500
        }), 500