#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
uWSGI 入口文件
避免与app包名冲突
"""

import sys
import os

# 确保项目路径在Python路径中
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

try:
    from app import create_app, db
    
    # 创建应用实例
    application = create_app()
    
    # 延迟数据库初始化
    def init_database():
        with application.app_context():
            try:
                db.create_all()
                print("数据库表初始化完成")
                return True
            except Exception as e:
                print(f"数据库表初始化失败: {e}")
                application.logger.error(f"数据库表初始化失败: {e}")
                return False
    
    # uWSGI模式下，立即初始化数据库
    # 避免使用 before_first_request，直接在这里初始化
    init_database()

except Exception as e:
    print(f"应用初始化失败: {e}")
    import traceback
    traceback.print_exc()
    
    # 创建一个简单的错误应用
    from flask import Flask
    application = Flask(__name__)
    
    @application.route('/')
    def error():
        return f"应用启动失败: {str(e)}", 500
    
    @application.route('/health')
    def health():
        return {'status': 'error', 'message': f'应用启动失败: {str(e)}'}, 500

# uWSGI会查找这个变量
app = application

if __name__ == "__main__":
    # 开发环境运行
    init_database()
    application.run(debug=application.config.get('DEBUG', False), host='0.0.0.0', port=5555)
