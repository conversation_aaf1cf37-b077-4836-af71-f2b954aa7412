#!/usr/bin/env python3
"""
告警去重管理工具

功能:
1. 分析现有告警的重复情况
2. 批量处理重复告警
3. 生成去重报告
4. 清理过期的重复告警

使用方法:
python scripts/alert_deduplication_tool.py [command] [options]

命令:
- analyze: 分析重复告警情况
- cleanup: 清理过期重复告警
- report: 生成去重报告
- merge: 合并重复告警
"""

import sys
import os
import argparse
from datetime import datetime, timedelta
from collections import defaultdict

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app, db
from app.models.alertmanager import AlertManagerLog


class AlertDeduplicationTool:
    """告警去重工具类"""
    
    def __init__(self):
        self.app = create_app()
        self.app_context = self.app.app_context()
        self.app_context.push()
    
    def __del__(self):
        if hasattr(self, 'app_context'):
            self.app_context.pop()
    
    def analyze_duplicates(self, days=7):
        """分析重复告警情况"""
        print(f"分析最近 {days} 天的告警重复情况...")
        
        # 查询指定时间范围内的告警
        start_date = datetime.utcnow() - timedelta(days=days)
        alerts = AlertManagerLog.query.filter(
            AlertManagerLog.created_at >= start_date
        ).all()
        
        # 按指纹分组统计
        fingerprint_groups = defaultdict(list)
        for alert in alerts:
            fingerprint_groups[alert.alert_fingerprint].append(alert)
        
        # 统计结果
        total_alerts = len(alerts)
        unique_alerts = len(fingerprint_groups)
        duplicate_groups = sum(1 for group in fingerprint_groups.values() if len(group) > 1)
        total_duplicates = sum(len(group) - 1 for group in fingerprint_groups.values() if len(group) > 1)
        
        print(f"\n=== 告警重复分析报告 ===")
        print(f"分析时间范围: {start_date.strftime('%Y-%m-%d %H:%M:%S')} - {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"总告警数: {total_alerts}")
        print(f"唯一告警数: {unique_alerts}")
        print(f"重复告警组数: {duplicate_groups}")
        print(f"重复告警总数: {total_duplicates}")
        print(f"去重率: {(total_duplicates / total_alerts * 100):.2f}%" if total_alerts > 0 else "0%")
        
        # 显示重复最多的告警
        print(f"\n=== 重复次数最多的告警 ===")
        sorted_groups = sorted(fingerprint_groups.items(), key=lambda x: len(x[1]), reverse=True)
        
        for i, (fingerprint, group) in enumerate(sorted_groups[:10]):
            if len(group) > 1:
                alert = group[0]
                print(f"{i+1}. {alert.alert_title[:50]}... (重复 {len(group)} 次)")
                print(f"   分类: {alert.project_category}, 等级: {alert.alert_level}")
                print(f"   指纹: {fingerprint}")
                print()
        
        return {
            'total_alerts': total_alerts,
            'unique_alerts': unique_alerts,
            'duplicate_groups': duplicate_groups,
            'total_duplicates': total_duplicates,
            'duplicate_rate': total_duplicates / total_alerts if total_alerts > 0 else 0
        }
    
    def cleanup_old_duplicates(self, days=30, dry_run=True, batch_size=100):
        """清理过期的重复告警"""
        print(f"清理 {days} 天前的重复告警...")

        cutoff_date = datetime.utcnow() - timedelta(days=days)

        # 查找过期的重复告警
        old_duplicates = AlertManagerLog.query.filter(
            AlertManagerLog.created_at < cutoff_date,
            AlertManagerLog.duplicate_count > 1,
            AlertManagerLog.status == 'resolved'
        ).all()

        print(f"找到 {len(old_duplicates)} 条过期重复告警")

        if not dry_run and old_duplicates:
            # 批量删除以提高性能
            deleted_count = 0
            for i in range(0, len(old_duplicates), batch_size):
                batch = old_duplicates[i:i + batch_size]
                for alert in batch:
                    db.session.delete(alert)

                db.session.commit()
                deleted_count += len(batch)
                print(f"已删除 {deleted_count}/{len(old_duplicates)} 条告警...")

            print(f"✅ 清理完成，共删除 {deleted_count} 条过期重复告警")

            # 记录清理日志
            with open('/var/log/alert_cleanup.log', 'a') as f:
                f.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - 清理了 {deleted_count} 条过期重复告警\n")

        elif dry_run:
            print("这是预览模式，没有实际删除数据")
            for alert in old_duplicates[:5]:  # 只显示前5条
                print(f"- {alert.alert_title[:50]}... (重复 {alert.duplicate_count} 次)")

        return len(old_duplicates)
    
    def merge_duplicates(self, fingerprint, dry_run=True):
        """合并指定指纹的重复告警"""
        print(f"合并指纹为 {fingerprint} 的重复告警...")
        
        # 查找相同指纹的告警
        alerts = AlertManagerLog.query.filter(
            AlertManagerLog.alert_fingerprint == fingerprint
        ).order_by(AlertManagerLog.created_at.asc()).all()
        
        if len(alerts) <= 1:
            print("没有找到重复告警")
            return
        
        print(f"找到 {len(alerts)} 条重复告警")
        
        # 保留最早的告警，合并其他告警的信息
        primary_alert = alerts[0]
        duplicate_alerts = alerts[1:]
        
        if not dry_run:
            # 更新主告警的重复次数和最后出现时间
            primary_alert.duplicate_count = len(alerts)
            primary_alert.last_occurrence = max(alert.last_occurrence for alert in alerts)
            
            # 删除重复告警
            for alert in duplicate_alerts:
                db.session.delete(alert)
            
            db.session.commit()
            print(f"已合并 {len(duplicate_alerts)} 条重复告警到主告警 ID: {primary_alert.id}")
        else:
            print("这是预览模式，没有实际合并数据")
            print(f"主告警: ID {primary_alert.id}, 创建时间: {primary_alert.created_at}")
            for alert in duplicate_alerts:
                print(f"- 重复告警: ID {alert.id}, 创建时间: {alert.created_at}")
    
    def generate_report(self, output_file=None):
        """生成详细的去重报告"""
        print("生成告警去重报告...")
        
        # 基础统计
        total_alerts = AlertManagerLog.query.count()
        duplicate_alerts = AlertManagerLog.query.filter(AlertManagerLog.duplicate_count > 1).count()
        total_duplicates = db.session.query(
            db.func.sum(AlertManagerLog.duplicate_count - 1)
        ).scalar() or 0
        
        # 按分类统计
        category_stats = db.session.query(
            AlertManagerLog.project_category,
            db.func.count(AlertManagerLog.id).label('count'),
            db.func.sum(AlertManagerLog.duplicate_count - 1).label('duplicates')
        ).group_by(AlertManagerLog.project_category).all()
        
        # 按等级统计
        level_stats = db.session.query(
            AlertManagerLog.alert_level,
            db.func.count(AlertManagerLog.id).label('count'),
            db.func.sum(AlertManagerLog.duplicate_count - 1).label('duplicates')
        ).group_by(AlertManagerLog.alert_level).all()
        
        # 生成报告内容
        report = f"""
=== 告警去重报告 ===
生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

基础统计:
- 总告警数: {total_alerts}
- 有重复的告警数: {duplicate_alerts}
- 总重复次数: {total_duplicates}
- 去重率: {(total_duplicates / total_alerts * 100):.2f}%

按项目分类统计:
"""
        
        for category, count, duplicates in category_stats:
            duplicates = duplicates or 0
            report += f"- {category or '未知'}: {count} 条告警, {duplicates} 次重复\n"
        
        report += "\n按告警等级统计:\n"
        for level, count, duplicates in level_stats:
            duplicates = duplicates or 0
            report += f"- {level or '未知'}: {count} 条告警, {duplicates} 次重复\n"
        
        print(report)
        
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report)
            print(f"报告已保存到: {output_file}")


def main():
    parser = argparse.ArgumentParser(description='告警去重管理工具')
    parser.add_argument('command', choices=['analyze', 'cleanup', 'report', 'merge'],
                       help='要执行的命令')
    parser.add_argument('--days', type=int, default=7,
                       help='分析或清理的天数 (默认: 7)')
    parser.add_argument('--fingerprint', type=str,
                       help='要合并的告警指纹')
    parser.add_argument('--output', type=str,
                       help='报告输出文件路径')
    parser.add_argument('--dry-run', action='store_true',
                       help='预览模式，不实际执行操作')
    
    args = parser.parse_args()
    
    tool = AlertDeduplicationTool()
    
    try:
        if args.command == 'analyze':
            tool.analyze_duplicates(args.days)
        elif args.command == 'cleanup':
            tool.cleanup_old_duplicates(args.days, args.dry_run)
        elif args.command == 'report':
            tool.generate_report(args.output)
        elif args.command == 'merge':
            if not args.fingerprint:
                print("错误: 合并命令需要指定 --fingerprint 参数")
                sys.exit(1)
            tool.merge_duplicates(args.fingerprint, args.dry_run)
    
    except Exception as e:
        print(f"执行失败: {str(e)}")
        sys.exit(1)


if __name__ == '__main__':
    main()
