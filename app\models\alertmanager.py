from app import db
from datetime import datetime
import hashlib
import json


class AlertManagerLog(db.Model):
    """AlertManager告警日志表 - 用于存储webhook接收的告警信息"""
    __tablename__ = 'alertmanager_logs'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    alert_title = db.Column(db.String(500), nullable=False, comment='告警标题')
    alert_description = db.Column(db.Text, nullable=True, comment='告警描述')
    project_owner = db.Column(db.String(200), nullable=False, default='徐世伟,郭亚彬', comment='项目负责人')
    alert_level = db.Column(db.String(50), nullable=True, comment='告警等级')
    project_category = db.Column(db.String(100), nullable=True, comment='项目分类')
    alert_time = db.Column(db.DateTime, nullable=True, comment='告警时间')
    recovery_time = db.Column(db.DateTime, nullable=True, comment='恢复时间')
    status = db.Column(db.String(20), nullable=False, default='active', comment='状态: active-活跃, resolved-已解决')
    raw_data = db.Column(db.Text, nullable=True, comment='原始告警数据JSON')

    # 新增去重相关字段
    alert_fingerprint = db.Column(db.String(32), nullable=False, comment='告警指纹，用于去重识别')
    duplicate_count = db.Column(db.Integer, default=1, comment='重复次数')
    last_occurrence = db.Column(db.DateTime, default=datetime.utcnow, comment='最后出现时间')

    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False, comment='更新时间')

    # 添加索引以提高查询性能
    __table_args__ = (
        db.Index('idx_alert_fingerprint', 'alert_fingerprint'),
        db.Index('idx_status_alert_time', 'status', 'alert_time'),
        db.Index('idx_project_category_level', 'project_category', 'alert_level'),
    )

    @staticmethod
    def generate_alert_fingerprint(alert_title, alert_description, project_category, alert_level, project_owner, alert_data=None):
        """生成告警指纹，用于去重识别"""
        # 获取去重配置
        config = AlertManagerLog._get_deduplication_config()
        fingerprint_fields = config.get('fingerprint_fields', ['alert_title', 'alert_description', 'project_category', 'alert_level', 'project_owner'])
        description_max_length = config.get('description_max_length', 200)

        # 构建指纹数据
        fingerprint_data = {}
        field_values = {
            'alert_title': alert_title,
            'alert_description': alert_description,
            'project_category': project_category,
            'alert_level': alert_level,
            'project_owner': project_owner
        }

        for field in fingerprint_fields:
            if field in field_values:
                value = field_values[field] or ''

                # 对描述字段进行长度限制
                if field == 'alert_description':
                    value = value[:description_max_length]

                # 标准化字段值
                normalized_value = AlertManagerLog._normalize_field_value(value, config)

                fingerprint_data[field] = normalized_value

        # 生成指纹
        fingerprint_str = json.dumps(fingerprint_data, sort_keys=True, ensure_ascii=False)
        return hashlib.md5(fingerprint_str.encode('utf-8')).hexdigest()

    @classmethod
    def find_or_create_alert(cls, alert_data, deduplication_window_hours=None):
        """查找或创建告警记录，实现去重逻辑"""
        # 提取告警数据
        alert_title = alert_data.get('alert_title', '')
        alert_description = alert_data.get('alert_description', '')
        project_owner = alert_data.get('project_owner', '徐世伟,郭亚彬')
        alert_level = alert_data.get('alert_level', '')
        project_category = alert_data.get('project_category', '')

        # 获取去重配置
        dedup_config = cls._get_deduplication_config()

        # 检查是否应该进行去重
        if not dedup_config.get('enabled', True):
            # 如果去重被禁用，直接创建新告警
            return cls._create_new_alert(alert_data), True

        # 获取去重时间窗口
        if deduplication_window_hours is None:
            window_hours = dedup_config.get('deduplication_window_hours', 24)
        else:
            window_hours = deduplication_window_hours

        from datetime import timedelta
        deduplication_window = timedelta(hours=window_hours)

        # 生成指纹
        fingerprint = cls.generate_alert_fingerprint(
            alert_title, alert_description, project_category, alert_level, project_owner, alert_data
        )

        # 在指定时间窗口内查找相同指纹的活跃告警
        window_start = datetime.utcnow() - deduplication_window

        existing_alert = cls.query.filter(
            cls.alert_fingerprint == fingerprint,
            cls.status == 'active',
            cls.created_at >= window_start
        ).first()

        if existing_alert:
            # 更新现有告警
            existing_alert.duplicate_count += 1
            existing_alert.last_occurrence = datetime.utcnow()
            existing_alert.updated_at = datetime.utcnow()

            # 如果新告警是恢复状态，更新现有告警
            if alert_data.get('recovery_time'):
                existing_alert.recovery_time = alert_data.get('recovery_time')
                existing_alert.status = 'resolved'

            return existing_alert, False  # 返回现有告警和是否为新创建的标志
        else:
            # 创建新告警
            new_alert = cls._create_new_alert(alert_data, fingerprint)
            return new_alert, True  # 返回新告警和是否为新创建的标志

    @classmethod
    def _create_new_alert(cls, alert_data, fingerprint=None):
        """创建新告警记录的辅助方法"""
        # 提取告警数据
        alert_title = alert_data.get('alert_title', '')
        alert_description = alert_data.get('alert_description', '')
        project_owner = alert_data.get('project_owner', '徐世伟,郭亚彬')
        alert_level = alert_data.get('alert_level', '')
        project_category = alert_data.get('project_category', '')

        # 如果没有提供指纹，生成一个
        if fingerprint is None:
            fingerprint = cls.generate_alert_fingerprint(
                alert_title, alert_description, project_category, alert_level, project_owner, alert_data
            )

        # 处理时间字段
        from datetime import datetime as dt
        alert_time = alert_data.get('alert_time')
        if isinstance(alert_time, str):
            # 如果是字符串，尝试解析
            try:
                alert_time = dt.strptime(alert_time, '%Y-%m-%d %H:%M:%S')
            except:
                alert_time = datetime.utcnow()
        elif not alert_time:
            alert_time = datetime.utcnow()

        recovery_time = alert_data.get('recovery_time')
        if isinstance(recovery_time, str):
            try:
                recovery_time = dt.strptime(recovery_time, '%Y-%m-%d %H:%M:%S')
            except:
                recovery_time = None

        status = 'resolved' if recovery_time else 'active'

        new_alert = cls(
            alert_title=alert_title,
            alert_description=alert_description,
            project_owner=project_owner,
            alert_level=alert_level,
            project_category=project_category,
            alert_time=alert_time,
            recovery_time=recovery_time,
            status=status,
            raw_data=cls._serialize_alert_data(alert_data),
            alert_fingerprint=fingerprint,
            duplicate_count=1,
            last_occurrence=datetime.utcnow()
        )

        return new_alert

    @classmethod
    def _get_deduplication_config(cls):
        """获取去重配置"""
        try:
            # 尝试从数据库获取配置
            from app.models.alert_config import AlertDeduplicationConfig
            config_record = AlertDeduplicationConfig.query.filter_by(is_active=True).first()

            if config_record:
                return {
                    'enabled': config_record.enabled,
                    'deduplication_window_hours': config_record.deduplication_window_hours,
                    'description_max_length': config_record.description_max_length,
                    'case_sensitive': config_record.case_sensitive,
                    'normalize_whitespace': config_record.normalize_whitespace,
                    'fingerprint_fields': config_record.fingerprint_fields.split(',') if config_record.fingerprint_fields else ['alert_title', 'alert_description', 'project_category', 'alert_level', 'project_owner']
                }
        except ImportError:
            pass
        except Exception as e:
            # 数据库查询失败，记录日志但不影响功能
            try:
                from flask import current_app
                current_app.logger.warning(f"获取去重配置失败，使用默认配置: {str(e)}")
            except:
                pass

        # 尝试从配置文件获取
        try:
            import json
            import os
            config_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config', 'deduplication_runtime_config.json')

            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    file_config = json.load(f)
                    return file_config
        except Exception:
            pass

        # 返回默认配置
        return {
            'enabled': True,
            'deduplication_window_hours': 24,
            'description_max_length': 200,
            'case_sensitive': False,
            'normalize_whitespace': True,
            'fingerprint_fields': ['alert_title', 'alert_description', 'project_category', 'alert_level', 'project_owner']
        }

    @staticmethod
    def _normalize_field_value(value, config):
        """标准化字段值"""
        if not value:
            return ''

        # 转换为字符串并去除首尾空白
        normalized = str(value).strip()

        # 标准化空白字符
        if config.get('normalize_whitespace', True):
            import re
            normalized = re.sub(r'\s+', ' ', normalized)

        # 大小写处理
        if not config.get('case_sensitive', False):
            normalized = normalized.lower()

        return normalized

    @classmethod
    def _serialize_alert_data(cls, alert_data):
        """序列化告警数据，处理datetime对象"""
        def datetime_handler(obj):
            """处理datetime对象的序列化"""
            if isinstance(obj, datetime):
                return obj.strftime('%Y-%m-%d %H:%M:%S')
            elif hasattr(obj, 'isoformat'):
                return obj.isoformat()
            raise TypeError(f"Object of type {type(obj)} is not JSON serializable")

        try:
            # 创建一个副本，避免修改原始数据
            serializable_data = {}
            for key, value in alert_data.items():
                if isinstance(value, datetime):
                    serializable_data[key] = value.strftime('%Y-%m-%d %H:%M:%S')
                elif isinstance(value, dict):
                    # 递归处理嵌套字典
                    serializable_data[key] = cls._make_serializable(value)
                elif isinstance(value, list):
                    # 递归处理列表
                    serializable_data[key] = [cls._make_serializable(item) if isinstance(item, (dict, list)) else item for item in value]
                else:
                    serializable_data[key] = value

            return json.dumps(serializable_data, ensure_ascii=False, indent=2, default=datetime_handler)
        except Exception as e:
            # 如果序列化失败，返回错误信息
            return json.dumps({
                'error': f'序列化失败: {str(e)}',
                'original_keys': list(alert_data.keys()) if isinstance(alert_data, dict) else str(type(alert_data))
            }, ensure_ascii=False, indent=2)

    @classmethod
    def _make_serializable(cls, obj):
        """递归地使对象可序列化"""
        if isinstance(obj, datetime):
            return obj.strftime('%Y-%m-%d %H:%M:%S')
        elif isinstance(obj, dict):
            return {key: cls._make_serializable(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [cls._make_serializable(item) for item in obj]
        else:
            return obj

    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'alert_title': self.alert_title,
            'alert_description': self.alert_description,
            'project_owner': self.project_owner,
            'alert_level': self.alert_level,
            'project_category': self.project_category,
            'alert_time': self._format_beijing_time(self.alert_time),
            'recovery_time': self._format_beijing_time(self.recovery_time),
            'status': self.status,
            'alert_fingerprint': self.alert_fingerprint,
            'duplicate_count': self.duplicate_count,
            'last_occurrence': self._format_beijing_time(self.last_occurrence),
            'created_at': self._format_beijing_time(self.created_at),
            'updated_at': self._format_beijing_time(self.updated_at)
        }

    def _format_beijing_time(self, dt):
        """将datetime对象格式化为北京时间字符串"""
        if not dt:
            return None

        try:
            from datetime import timezone, timedelta

            # 如果没有时区信息，假设是UTC时间
            if dt.tzinfo is None:
                dt = dt.replace(tzinfo=timezone.utc)

            # 转换为北京时间 (UTC+8)
            beijing_tz = timezone(timedelta(hours=8))
            beijing_time = dt.astimezone(beijing_tz)

            return beijing_time.strftime('%Y-%m-%d %H:%M:%S')

        except Exception:
            # 如果转换失败，返回原格式
            return dt.strftime('%Y-%m-%d %H:%M:%S') if dt else None
