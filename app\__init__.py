from flask import Flask, g
from flask_login import Lo<PERSON><PERSON>anager
from datetime import timedelta
import config
from flask_jwt_extended import J<PERSON><PERSON>anager
from app.utils.db import get_session, close_session, db

jwt = JWTManager()
login_manager = LoginManager()


def create_app():
    app = Flask(__name__)

    # 配置加载
    app.config.from_object('config')
    app.config.update(
        SECRET_KEY=config.SECRET_KEY,
        DEBUG=config.DEBUG,
        SQLALCHEMY_DATABASE_URI=config.SQLALCHEMY_DATABASE_URI,
        SQLALCHEMY_TRACK_MODIFICATIONS=config.SQLALCHEMY_TRACK_MODIFICATIONS,
        SQLALCHEMY_POOL_RECYCLE=config.SQLALCHEMY_POOL_RECYCLE,
        SQLALCHEMY_POOL_SIZE=config.SQLALCHEMY_POOL_SIZE,
        SQLALCHEMY_MAX_OVERFLOW=config.SQLALCHEMY_MAX_OVERFLOW,
        SQLALCHEMY_POOL_TIMEOUT=config.SQLALCHEMY_POOL_TIMEOUT,
        REMEMBER_COOKIE_DURATION=timedelta(hours=4),
        REMEMBER_COOKIE_HTTPONLY=True,
        REMEMBER_COOKIE_SECURE=False,
        JWT_SECRET_KEY=config.JWT_SECRET_KEY,
        JWT_TOKEN_LOCATION=config.JWT_TOKEN_LOCATION,
        JWT_ACCESS_TOKEN_EXPIRES=config.JWT_ACCESS_TOKEN_EXPIRES,
        JWT_HEADER_NAME=config.JWT_HEADER_NAME,
        JWT_HEADER_TYPE=config.JWT_HEADER_TYPE,
        JWT_ALGORITHM=config.JWT_ALGORITHM
    )

    jwt.init_app(app)
    db.init_app(app)

    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'

    @login_manager.user_loader
    def load_user(user_id):
        from app.models.user import User  # 延迟导入避免循环依赖
        return User.query.get(int(user_id))

    @jwt.user_lookup_loader
    def user_lookup_callback(jwt_header, jwt_data):
        from app.models.user import User
        identity = jwt_data["sub"]
        session = get_session()
        try:
            return session.query(User).get(identity)
        finally:
            close_session(session)

    @app.teardown_request
    def teardown_request(exception=None):
        # 确保请求结束时清理所有数据库资源
        if hasattr(g, 'db_session'):
            close_session(g.db_session)

    # 注册蓝图
    from app.routes.auth import auth_bp
    from app.routes.user import user_bp
    from app.routes.menu import menu_bp
    from app.routes.system import usermanage_bp
    from app.routes.dbsync import dbsync_bp
    from app.routes.logalert import logalert_bp
    from app.routes.fail2ban import fail2ban_bp
    from app.routes.nginx import nginx_bp
    from app.routes.oracle import oracle_bp
    from app.routes.link import link_bp
    from app.routes.redis_cluster import redis_bp  # 新增Redis路由
    from app.routes.wiki import wiki_bp  # 新增Wiki路由
    from app.routes.apollo import apollo_bp  # 新增Apollo路由
    from app.routes.permission import permission_bp  # 新增权限管理路由
    from app.routes.alertmanager import alertmanager_bp  # 新增AlertManager路由

    app.register_blueprint(auth_bp)
    app.register_blueprint(user_bp)
    app.register_blueprint(menu_bp)
    app.register_blueprint(usermanage_bp)
    app.register_blueprint(dbsync_bp)
    app.register_blueprint(logalert_bp)
    app.register_blueprint(fail2ban_bp)
    app.register_blueprint(nginx_bp)
    app.register_blueprint(oracle_bp)
    app.register_blueprint(link_bp)
    app.register_blueprint(redis_bp)
    app.register_blueprint(wiki_bp)
    app.register_blueprint(apollo_bp)
    app.register_blueprint(permission_bp)
    app.register_blueprint(alertmanager_bp)  # 注册AlertManager蓝图

    return app
